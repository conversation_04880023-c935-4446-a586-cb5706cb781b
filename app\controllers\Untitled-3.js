!function(n){var i={};function r(e){var t;return(i[e]||(t=i[e]={i:e,l:!1,exports:{}},n[e].call(t.exports,t,t.exports,r),t.l=!0,t)).exports}r.m=n,r.c=i,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=131)}([,,,function(e,t){var n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},,function(module,exports,__webpack_require__){"use strict";var __assign=this&&this.__assign||function(){return(__assign=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},GetQueryParameters_1=(exports.__esModule=!0,exports.loadScript=exports.jsonp=exports.xml=exports.json=exports.text=exports.post=exports.get=exports.ajax=exports.ResType=exports.serialize=void 0,__webpack_require__(6)),getUin_1=__webpack_require__(7),hasOwnProperty=Object.prototype.hasOwnProperty,CryptoJS=__webpack_require__(11),platform=__webpack_require__(45);function forEach(e,t,n){if(e)if(e.length&&e.length===+e.length){for(var i=0;i<e.length;i++)if(!0===t.call(n,e[i],i,e))return}else for(var r in e)if(hasOwnProperty.call(e,r)&&!0===t.call(n,e[r],r,e))return}var Types={},_toString=Object.prototype.toString;function serialize(e,i){var r=[],e=(forEach(e,function(e,n){Types.isArray(e)?forEach(e,function(e,t){r.push(n+"="+(i?e:encodeURIComponent(e)))}):r.push(n+"="+(i?e:encodeURIComponent(e)))}),r.join("&"));return i?e+"F#E#@WEB$#FA@#@DS":e}function parseJSON(t){try{return JSON.parse(t)}catch(e){try{return new Function("return "+t)()}catch(e){}}return null}forEach(["Array","Boolean","Function","Object","String","Number"],function(t){Types["is"+t]=function(e){return _toString.call(e)==="[object "+t+"]"}}),exports.serialize=serialize;var createXHR="XMLHttpRequest"in window?function(){return new XMLHttpRequest}:function(){return new window.ActiveXObject("Microsoft.XMLHTTP")},ResType;function ajax(p){return new Promise(function(r,o){var e,t=p=Types.isString(p)?{url:p}:p,n=t.url,i=t.method,i=void 0===i?"GET":i,a=t.data,s=t.type,c=void 0===s?ResType.JSON:s,s=t.timeout,s=void 0===s?3e4:s,l=t.credential,u=t.encode,u=void 0===u?"UTF-8":u,t=t.file,i=i.toUpperCase(),f=(Types.isObject(a)&&(e=serialize(a)),"GET"===i&&e&&(n+=(-1===n.indexOf("?")?"?":"&")+e),createXHR());if(!f)return null;var d,h=!1;0<s&&(d=setTimeout(function(){h=!0,f.abort()},s)),f.onreadystatechange=function(){if(4===f.readyState)if(h)o(new Error("request timeout"));else{var e=f,t=c,n=e.status;if(200<=n&&n<300){var i=void 0;switch(t){case ResType.TEXT:i=e.responseText;break;case ResType.JSON:i=parseJSON(e.responseText);break;case ResType.XML:i=e.responseXML}void 0!==i&&r(i,n,e)}else o(new Error(e.status+""));e=null,clearTimeout(d)}},f.open(i,n,!0),l&&(f.withCredentials=!0),t?f.send(a):("POST"===i&&f.setRequestHeader("Content-type","application/x-www-form-urlencoded;charset="+u),f.send(e))})}!function(e){e["TEXT"]="text",e["JSON"]="json",e["XML"]="xml"}(ResType=exports.ResType||(exports.ResType={})),exports.ajax=ajax;var api={method:["get","post"],type:["text","json","xml"]},Shorthand={};function get(e){return Shorthand["get"](e)}function post(e){return Shorthand["post"](e)}function text(e){return Shorthand["text"](e)}function json(e){return Shorthand["json"](e)}function xml(e){return Shorthand["xml"](e)}function generateRandomName(){for(var e=[],t="0123456789ABCDEF",n=0;n<32;n++)e[n]=t.substr(Math.floor(16*Math.random()),1);return e[12]="4",e[16]=t.substr(3&e[16]|8,1),"jsonp_"+e.join("")}forEach(api,function(e,i){forEach(e,function(e){var t,n;Shorthand[e]=(t=i,n=e,function(e){return(e=Types.isString(e)?{url:e}:e)[t]=n,ajax(e)})})}),exports.get=get,exports.post=post,exports.text=text,exports.json=json,exports.xml=xml;var ie678=eval("!-[1,]"),head=document.head||document.getElementsByTagName("head")[0];function jsonpCore(h){return new Promise(function(t,n){var e=h,i=e.url,r=e.data,r=void 0===r?{}:r,o=e.timestamp,o=void 0!==o&&o,a=e.timeout,a=void 0===a?3e4:a,s=e.jsonName,s=void 0===s?"callback":s,c=e.jsonpCallback,l=void 0===c?generateRandomName():c,c=e.charset,u=document.createElement("script"),f=!1;function d(e){(e=void 0===e?!1:e)?f=!0:n(new Error("network error.")),u.onload=u.onerror=u.onreadystatechange=null,head&&u.parentNode&&(head.removeChild(u),u=null,window[l]=void 0)}ie678?u.onreadystatechange=function(){var e=this.readyState;f||"loaded"!=e&&"complete"!=e||d(!0)}:(u.onload=function(){d(!0)},u.onerror=function(){d()},window.opera&&setTimeout(function(){f||d()},a)),c&&(u.charset=c);a=serialize(__assign(__assign({},r),((e={device:platform.os.family+" "+platform.os.version+" "+platform.name,product:"WEB",zcid:(0,getUin_1["default"])()})[s]=l,e)));-1<(i+=(-1===i.indexOf("?")?"?":"&")+a+(o?"&ts="+(new Date).getTime():"")).indexOf("?")&&(delete(c=(0,GetQueryParameters_1["default"])(i.slice(i.indexOf("?"))))[s],i+="&sign="+CryptoJS.MD5(serialize(c,!0))),window[l]=function(e){t(e)},u.src=i,head.insertBefore(u,head.firstChild)})}function jsonp(e){return jsonpCore(e=Types.isString(e)?{url:e}:e)}function loadScript(e,t){var n=document,i=n.head||n.getElementsByTagName("head")[0],r=n.createElement("script");r.src=e,r.onload=r["onreadystatechange"]=function(){this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(t&&t(),r.onload=r["onreadystatechange"]=null,i&&r.parentNode&&i.removeChild(r))},i.insertBefore(r,i.firstChild)}exports.jsonp=jsonp,exports.loadScript=loadScript},function(e,t,n){"use strict";t.__esModule=!0,t["default"]=function(e){var t=(e||document.location.search).replace(/(^\?)/,"").split("&"),n={};if(t.map)return t.map(function(e){return this[(e=e.split("="))[0]]=decodeURIComponent("string"!=typeof e[1]?JSON.stringify(e[1]):e[1]),this}.bind({}))[0];for(var i=0,r=t.length;i<r;i++){var o=t[i].split("=");"string"!=typeof o[1]&&(o[1]=JSON.stringify(o[1])),n[o[0]]=decodeURIComponent(o[1])}return n}},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,a,s,c){return new(s=s||Promise)(function(n,t){function i(e){try{o(c.next(e))}catch(e){t(e)}}function r(e){try{o(c["throw"](e))}catch(e){t(e)}}function o(e){var t;e.done?n(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(i,r)}o((c=c.apply(e,a||[])).next())})},r=this&&this.__generator||function(i,r){var o,a,s,c={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},e={next:t(0),"throw":t(1),"return":t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(n){return function(e){var t=[n,e];if(o)throw new TypeError("Generator is already executing.");for(;c;)try{if(o=1,a&&(s=2&t[0]?a["return"]:t[0]?a["throw"]||((s=a["return"])&&s.call(a),0):a.next)&&!(s=s.call(a,t[1])).done)return s;switch(a=0,(t=s?[2&t[0],s.value]:t)[0]){case 0:case 1:s=t;break;case 4:return c.label++,{value:t[1],done:!1};case 5:c.label++,a=t[1],t=[0];continue;case 7:t=c.ops.pop(),c.trys.pop();continue;default:if(!(s=0<(s=c.trys).length&&s[s.length-1])&&(6===t[0]||2===t[0])){c=0;continue}if(3===t[0]&&(!s||t[1]>s[0]&&t[1]<s[3]))c.label=t[1];else if(6===t[0]&&c.label<s[1])c.label=s[1],s=t;else{if(!(s&&c.label<s[2])){s[2]&&c.ops.pop(),c.trys.pop();continue}c.label=s[2],c.ops.push(t)}}t=r.call(i,c)}catch(e){t=[6,e],a=0}finally{o=s=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}}},o=(t.__esModule=!0,n(8)),a=n(9),s="_UINIDFLASH";!function(){i(this,void 0,void 0,function(){var t,n;return r(this,function(e){switch(e.label){case 0:return t=function(){return new Promise(function(t){function e(){a.get(function(e){e=e.map(function(e){return e.value}),e=a.x64hash128(e.join(""),31);t(e)})}window.requestIdleCallback?window.requestIdleCallback(e):setTimeout(e,250)})},(n=o.SiteCookie.get(s))?[3,2]:[4,t()];case 1:n=e.sent(),o.SiteCookie.set(s,n),e.label=2;case 2:return[2,n]}})})}(),t["default"]=function(){return o.SiteCookie.get(s)}},function(e,t,n){"use strict";t.__esModule=!0,t.SiteCookie=void 0;var o={_expires:2592e6,_domain:".flash.cn",set:function(e,t,n,i,r){n=new Date((new Date).getTime()+(n||o._expires)),document.cookie=e+"="+escape(t)+";expires="+n.toGMTString()+";path="+(i||"/")+";domain="+(r||o._domain)},get:function(e){e=document.cookie.match(new RegExp("(^| )"+e+"=([^;]*)(;|$)"));return null!=e?unescape(e[2]):null},clear:function(e,t,n){this.get(e)&&(document.cookie=e+"=; path="+(t||"/")+"; domain="+(n||o._domain)+"; expires=Fri, 02-Jan-1970 00:00:00 GMT")}};t.SiteCookie=o},function(n,i,r){var o;!function(e,t){"use strict";"undefined"!=typeof window&&r(10)?void 0!==(o="function"==typeof(o=t)?o.call(i,r,i,n):o)&&(n.exports=o):n.exports?n.exports=t():e.exports?e.exports=t():e["Fingerprint2"]=t()}(this,function(){"use strict";void 0===Array.isArray&&(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)});function f(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var n=[0,0,0,0];return n[3]+=e[3]+t[3],n[2]+=n[3]>>>16,n[3]&=65535,n[2]+=e[2]+t[2],n[1]+=n[2]>>>16,n[2]&=65535,n[1]+=e[1]+t[1],n[0]+=n[1]>>>16,n[1]&=65535,n[0]+=e[0]+t[0],n[0]&=65535,[n[0]<<16|n[1],n[2]<<16|n[3]]}function d(e,t){return 32===(t%=64)?[e[1],e[0]]:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t|e[0]>>>32-t]:[e[1]<<(t-=32)|e[0]>>>32-t,e[0]<<t|e[1]>>>32-t]}function h(e,t){return 0===(t%=64)?e:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t]:[e[1]<<t-32,0]}function p(e){return e=v(e,[0,e[0]>>>1]),e=g(e,[4283543511,3981806797]),e=v(e,[0,e[0]>>>1]),e=g(e,[3301882366,444984403]),e=v(e,[0,e[0]>>>1])}function s(e,t){for(var n=(e=e||"").length%16,i=e.length-n,r=[0,t=t||0],o=[0,t],a=[0,0],s=[0,0],c=[2277735313,289559509],l=[1291169091,658871167],u=0;u<i;u+=16)a=[255&e.charCodeAt(u+4)|(255&e.charCodeAt(u+5))<<8|(255&e.charCodeAt(u+6))<<16|(255&e.charCodeAt(u+7))<<24,255&e.charCodeAt(u)|(255&e.charCodeAt(u+1))<<8|(255&e.charCodeAt(u+2))<<16|(255&e.charCodeAt(u+3))<<24],s=[255&e.charCodeAt(u+12)|(255&e.charCodeAt(u+13))<<8|(255&e.charCodeAt(u+14))<<16|(255&e.charCodeAt(u+15))<<24,255&e.charCodeAt(u+8)|(255&e.charCodeAt(u+9))<<8|(255&e.charCodeAt(u+10))<<16|(255&e.charCodeAt(u+11))<<24],a=g(a,c),a=d(a,31),a=g(a,l),r=v(r,a),r=d(r,27),r=f(r,o),r=f(g(r,[0,5]),[0,1390208809]),s=g(s,l),s=d(s,33),s=g(s,c),o=v(o,s),o=d(o,31),o=f(o,r),o=f(g(o,[0,5]),[0,944331445]);switch(a=[0,0],s=[0,0],n){case 15:s=v(s,h([0,e.charCodeAt(u+14)],48));case 14:s=v(s,h([0,e.charCodeAt(u+13)],40));case 13:s=v(s,h([0,e.charCodeAt(u+12)],32));case 12:s=v(s,h([0,e.charCodeAt(u+11)],24));case 11:s=v(s,h([0,e.charCodeAt(u+10)],16));case 10:s=v(s,h([0,e.charCodeAt(u+9)],8));case 9:s=v(s,[0,e.charCodeAt(u+8)]),s=g(s,l),s=d(s,33),s=g(s,c),o=v(o,s);case 8:a=v(a,h([0,e.charCodeAt(u+7)],56));case 7:a=v(a,h([0,e.charCodeAt(u+6)],48));case 6:a=v(a,h([0,e.charCodeAt(u+5)],40));case 5:a=v(a,h([0,e.charCodeAt(u+4)],32));case 4:a=v(a,h([0,e.charCodeAt(u+3)],24));case 3:a=v(a,h([0,e.charCodeAt(u+2)],16));case 2:a=v(a,h([0,e.charCodeAt(u+1)],8));case 1:a=v(a,[0,e.charCodeAt(u)]),a=g(a,c),a=d(a,31),a=g(a,l),r=v(r,a)}return r=v(r,[0,e.length]),o=v(o,[0,e.length]),r=f(r,o),o=f(o,r),r=p(r),o=p(o),r=f(r,o),o=f(o,r),("00000000"+(r[0]>>>0).toString(16)).slice(-8)+("00000000"+(r[1]>>>0).toString(16)).slice(-8)+("00000000"+(o[0]>>>0).toString(16)).slice(-8)+("00000000"+(o[1]>>>0).toString(16)).slice(-8)}function n(){if(a()&&window.WebGLRenderingContext){var e=w();if(e){try{S(e)}catch(e){}return 1}}}function i(e){throw new Error("'new Fingerprint()' is deprecated, see https://github.com/fingerprintjs/fingerprintjs#upgrade-guide-from-182-to-200")}var g=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var n=[0,0,0,0];return n[3]+=e[3]*t[3],n[2]+=n[3]>>>16,n[3]&=65535,n[2]+=e[2]*t[3],n[1]+=n[2]>>>16,n[2]&=65535,n[2]+=e[3]*t[2],n[1]+=n[2]>>>16,n[2]&=65535,n[1]+=e[1]*t[3],n[0]+=n[1]>>>16,n[1]&=65535,n[1]+=e[2]*t[2],n[0]+=n[1]>>>16,n[1]&=65535,n[1]+=e[3]*t[1],n[0]+=n[1]>>>16,n[1]&=65535,n[0]+=e[0]*t[3]+e[1]*t[2]+e[2]*t[1]+e[3]*t[0],n[0]&=65535,[n[0]<<16|n[1],n[2]<<16|n[3]]},v=function(e,t){return[e[0]^t[0],e[1]^t[1]]},l={preprocessor:null,audio:{timeout:1e3,excludeIOS11:!0},fonts:{swfContainerId:"fingerprintjs2",swfPath:"flash/compiled/FontList.swf",userDefinedFonts:[],extendedJsFonts:!1},screen:{detectScreenOrientation:!0},plugins:{sortPluginsFor:[/palemoon/i],excludeIE:!1},extraComponents:[],excludes:{"enumerateDevices":!0,"pixelRatio":!0,"doNotTrack":!0,"fontsFlash":!0,"adBlock":!0},NOT_AVAILABLE:"not available",ERROR:"error",EXCLUDED:"excluded"},y=function(e,t){if(Array.prototype.forEach&&e.forEach===Array.prototype.forEach)e.forEach(t);else if(e.length===+e.length)for(var n=0,i=e.length;n<i;n++)t(e[n],n,e);else for(var r in e)e.hasOwnProperty(r)&&t(e[r],r,e)},c=function(e,i){var r=[];if(null!=e){if(Array.prototype.map&&e.map===Array.prototype.map)return e.map(i);y(e,function(e,t,n){r.push(i(e,t,n))})}return r},r=function(e){if(null==navigator.plugins)return e.NOT_AVAILABLE;for(var t=[],n=0,i=navigator.plugins.length;n<i;n++)navigator.plugins[n]&&t.push(navigator.plugins[n]);return o(e)&&(t=t.sort(function(e,t){return e.name>t.name?1:e.name<t.name?-1:0})),c(t,function(e){var t=c(e,function(e){return[e.type,e.suffixes]});return[e.name,e.description,t]})},o=function(e){for(var t=!1,n=0,i=e.plugins.sortPluginsFor.length;n<i;n++){var r=e.plugins.sortPluginsFor[n];if(navigator.userAgent.match(r)){t=!0;break}}return t},a=function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},u=function(){return 2<=("msWriteProfilerMark"in window)+("msLaunchUri"in navigator)+("msSaveBlob"in navigator)},m=function(e){var t=document.createElement("div");t.setAttribute("id",e.fonts.swfContainerId),document.body.appendChild(t)},w=function(){var e=document.createElement("canvas"),t=null;try{t=e.getContext("webgl")||e.getContext("experimental-webgl")}catch(e){}return t=t||null},S=function(e){e=e.getExtension("WEBGL_lose_context");null!=e&&e.loseContext()},_=[{key:"userAgent",getData:function(e){e(navigator.userAgent)}},{key:"webdriver",getData:function(e,t){e(null==navigator.webdriver?t.NOT_AVAILABLE:navigator.webdriver)}},{key:"language",getData:function(e,t){e(navigator.language||navigator.userLanguage||navigator.browserLanguage||navigator.systemLanguage||t.NOT_AVAILABLE)}},{key:"colorDepth",getData:function(e,t){e(window.screen.colorDepth||t.NOT_AVAILABLE)}},{key:"deviceMemory",getData:function(e,t){e(navigator.deviceMemory||t.NOT_AVAILABLE)}},{key:"pixelRatio",getData:function(e,t){e(window.devicePixelRatio||t.NOT_AVAILABLE)}},{key:"hardwareConcurrency",getData:function(e,t){e(function(e){if(navigator.hardwareConcurrency)return navigator.hardwareConcurrency;return e.NOT_AVAILABLE}(t))}},{key:"screenResolution",getData:function(e,t){e(function(e){var t=[window.screen.width,window.screen.height];if(e.screen.detectScreenOrientation)t.sort().reverse();return t}(t))}},{key:"availableScreenResolution",getData:function(e,t){e(function(e){if(window.screen.availWidth&&window.screen.availHeight){var t=[window.screen.availHeight,window.screen.availWidth];if(e.screen.detectScreenOrientation)t.sort().reverse();return t}return e.NOT_AVAILABLE}(t))}},{key:"timezoneOffset",getData:function(e){e((new Date).getTimezoneOffset())}},{key:"timezone",getData:function(e,t){window.Intl&&window.Intl.DateTimeFormat?e((new window.Intl.DateTimeFormat).resolvedOptions().timeZone||t.NOT_AVAILABLE):e(t.NOT_AVAILABLE)}},{key:"sessionStorage",getData:function(e,t){e(function(t){try{return!!window.sessionStorage}catch(e){return t.ERROR}}(t))}},{key:"localStorage",getData:function(e,t){e(function(t){try{return!!window.localStorage}catch(e){return t.ERROR}}(t))}},{key:"indexedDb",getData:function(e,t){e(function(t){if(u())return t.EXCLUDED;try{return!!window.indexedDB}catch(e){return t.ERROR}}(t))}},{key:"addBehavior",getData:function(e){e(!!window.HTMLElement.prototype.addBehavior)}},{key:"openDatabase",getData:function(e){e(!!window.openDatabase)}},{key:"cpuClass",getData:function(e,t){e((e=t,navigator.cpuClass||e.NOT_AVAILABLE))}},{key:"platform",getData:function(e,t){e(function(e){if(navigator.platform)return navigator.platform;else return e.NOT_AVAILABLE}(t))}},{key:"doNotTrack",getData:function(e,t){e(function(e){if(navigator.doNotTrack)return navigator.doNotTrack;else if(navigator.msDoNotTrack)return navigator.msDoNotTrack;else if(window.doNotTrack)return window.doNotTrack;else return e.NOT_AVAILABLE}(t))}},{key:"plugins",getData:function(e,t){!function(){if(navigator.appName==="Microsoft Internet Explorer")return true;else if(navigator.appName==="Netscape"&&/Trident/.test(navigator.userAgent))return true;return false}()?e(r(t)):t.plugins.excludeIE?e(t.EXCLUDED):e(function(t){var e=[];if(Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(window,"ActiveXObject")||"ActiveXObject"in window){var n=["AcroPDF.PDF","Adodb.Stream","AgControl.AgControl","DevalVRXCtrl.DevalVRXCtrl.1","MacromediaFlashPaper.MacromediaFlashPaper","Msxml2.DOMDocument","Msxml2.XMLHTTP","PDF.PdfCtrl","QuickTime.QuickTime","QuickTimeCheckObject.QuickTimeCheck.1","RealPlayer","RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)","RealVideo.RealVideo(tm) ActiveX Control (32-bit)","Scripting.Dictionary","SWCtl.SWCtl","Shell.UIHelper","ShockwaveFlash.ShockwaveFlash","Skype.Detection","TDCCtl.TDCCtl","WMPlayer.OCX","rmocx.RealPlayer G2 Control","rmocx.RealPlayer G2 Control.1"];e=c(n,function(e){try{new window.ActiveXObject(e);return e}catch(e){return t.ERROR}})}else e.push(t.NOT_AVAILABLE);if(navigator.plugins)e=e.concat(r(t));return e}(t))}},{key:"canvas",getData:function(e,t){a()?e(function(e){var t=[],n=document.createElement("canvas"),i=(n.width=2e3,n.height=200,n.style.display="inline",n.getContext("2d"));if(i.rect(0,0,10,10),i.rect(2,2,6,6),t.push("canvas winding:"+(i.isPointInPath(5,5,"evenodd")===false?"yes":"no")),i.textBaseline="alphabetic",i.fillStyle="#f60",i.fillRect(125,1,62,20),i.fillStyle="#069",e.dontUseFakeFontInCanvas)i.font="11pt Arial";else i.font="11pt no-real-font-123";if(i.fillText("Cwm fjordbank glyphs vext quiz, \ud83d\ude03",2,15),i.fillStyle="rgba(102, 204, 0, 0.2)",i.font="18pt Arial",i.fillText("Cwm fjordbank glyphs vext quiz, \ud83d\ude03",4,45),i.globalCompositeOperation="multiply",i.fillStyle="rgb(255,0,255)",i.beginPath(),i.arc(50,50,50,0,Math.PI*2,true),i.closePath(),i.fill(),i.fillStyle="rgb(0,255,255)",i.beginPath(),i.arc(100,50,50,0,Math.PI*2,true),i.closePath(),i.fill(),i.fillStyle="rgb(255,255,0)",i.beginPath(),i.arc(75,100,50,0,Math.PI*2,true),i.closePath(),i.fill(),i.fillStyle="rgb(255,0,255)",i.arc(75,75,75,0,Math.PI*2,true),i.arc(75,75,25,0,Math.PI*2,true),i.fill("evenodd"),n.toDataURL)t.push("canvas fp:"+n.toDataURL());return t}(t)):e(t.NOT_AVAILABLE)}},{key:"webgl",getData:function(e,t){n()?e(function(){var a,e=function(e){a.clearColor(0,0,0,1);a.enable(a.DEPTH_TEST);a.depthFunc(a.LEQUAL);a.clear(a.COLOR_BUFFER_BIT|a.DEPTH_BUFFER_BIT);return"["+e[0]+", "+e[1]+"]"},t=function(e){var t=e.getExtension("EXT_texture_filter_anisotropic")||e.getExtension("WEBKIT_EXT_texture_filter_anisotropic")||e.getExtension("MOZ_EXT_texture_filter_anisotropic");if(t){var n=e.getParameter(t.MAX_TEXTURE_MAX_ANISOTROPY_EXT);if(n===0)n=2;return n}else return null},a=w();if(!a)return null;try{var s=[];var n="attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}";var i="precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}";var r=a.createBuffer();a.bindBuffer(a.ARRAY_BUFFER,r);var o=new Float32Array([-.2,-.9,0,.4,-.26,0,0,.732134444,0]);a.bufferData(a.ARRAY_BUFFER,o,a.STATIC_DRAW);r.itemSize=3;r.numItems=3;var c=a.createProgram();var l=a.createShader(a.VERTEX_SHADER);a.shaderSource(l,n);a.compileShader(l);var u=a.createShader(a.FRAGMENT_SHADER);a.shaderSource(u,i);a.compileShader(u);a.attachShader(c,l);a.attachShader(c,u);a.linkProgram(c);a.useProgram(c);c.vertexPosAttrib=a.getAttribLocation(c,"attrVertex");c.offsetUniform=a.getUniformLocation(c,"uniformOffset");a.enableVertexAttribArray(c.vertexPosArray);a.vertexAttribPointer(c.vertexPosAttrib,r.itemSize,a.FLOAT,!1,0,0);a.uniform2f(c.offsetUniform,1,1);a.drawArrays(a.TRIANGLE_STRIP,0,r.numItems);try{s.push(a.canvas.toDataURL())}catch(e){}s.push("extensions:"+(a.getSupportedExtensions()||[]).join(";"));s.push("webgl aliased line width range:"+e(a.getParameter(a.ALIASED_LINE_WIDTH_RANGE)));s.push("webgl aliased point size range:"+e(a.getParameter(a.ALIASED_POINT_SIZE_RANGE)));s.push("webgl alpha bits:"+a.getParameter(a.ALPHA_BITS));s.push("webgl antialiasing:"+(a.getContextAttributes().antialias?"yes":"no"));s.push("webgl blue bits:"+a.getParameter(a.BLUE_BITS));s.push("webgl depth bits:"+a.getParameter(a.DEPTH_BITS));s.push("webgl green bits:"+a.getParameter(a.GREEN_BITS));s.push("webgl max anisotropy:"+t(a));s.push("webgl max combined texture image units:"+a.getParameter(a.MAX_COMBINED_TEXTURE_IMAGE_UNITS));s.push("webgl max cube map texture size:"+a.getParameter(a.MAX_CUBE_MAP_TEXTURE_SIZE));s.push("webgl max fragment uniform vectors:"+a.getParameter(a.MAX_FRAGMENT_UNIFORM_VECTORS));s.push("webgl max render buffer size:"+a.getParameter(a.MAX_RENDERBUFFER_SIZE));s.push("webgl max texture image units:"+a.getParameter(a.MAX_TEXTURE_IMAGE_UNITS));s.push("webgl max texture size:"+a.getParameter(a.MAX_TEXTURE_SIZE));s.push("webgl max varying vectors:"+a.getParameter(a.MAX_VARYING_VECTORS));s.push("webgl max vertex attribs:"+a.getParameter(a.MAX_VERTEX_ATTRIBS));s.push("webgl max vertex texture image units:"+a.getParameter(a.MAX_VERTEX_TEXTURE_IMAGE_UNITS));s.push("webgl max vertex uniform vectors:"+a.getParameter(a.MAX_VERTEX_UNIFORM_VECTORS));s.push("webgl max viewport dims:"+e(a.getParameter(a.MAX_VIEWPORT_DIMS)));s.push("webgl red bits:"+a.getParameter(a.RED_BITS));s.push("webgl renderer:"+a.getParameter(a.RENDERER));s.push("webgl shading language version:"+a.getParameter(a.SHADING_LANGUAGE_VERSION));s.push("webgl stencil bits:"+a.getParameter(a.STENCIL_BITS));s.push("webgl vendor:"+a.getParameter(a.VENDOR));s.push("webgl version:"+a.getParameter(a.VERSION));try{var f=a.getExtension("WEBGL_debug_renderer_info");if(f){s.push("webgl unmasked vendor:"+a.getParameter(f.UNMASKED_VENDOR_WEBGL));s.push("webgl unmasked renderer:"+a.getParameter(f.UNMASKED_RENDERER_WEBGL))}}catch(e){}if(!a.getShaderPrecisionFormat)return s;y(["FLOAT","INT"],function(o){y(["VERTEX","FRAGMENT"],function(r){y(["HIGH","MEDIUM","LOW"],function(i){y(["precision","rangeMin","rangeMax"],function(e){var t=a.getShaderPrecisionFormat(a[r+"_SHADER"],a[i+"_"+o])[e];if(e!=="precision")e="precision "+e;var n=["webgl ",r.toLowerCase()," shader ",i.toLowerCase()," ",o.toLowerCase()," ",e,":",t].join("");s.push(n)})})})});return s}finally{try{S(a)}catch(e){}}}()):e(t.NOT_AVAILABLE)}},{key:"webglVendorAndRenderer",getData:function(e){n()?e(function(){try{var e=w();var t=e.getExtension("WEBGL_debug_renderer_info");return e.getParameter(t.UNMASKED_VENDOR_WEBGL)+"~"+e.getParameter(t.UNMASKED_RENDERER_WEBGL)}catch(e){return null}finally{try{S(e)}catch(e){}}}()):e()}},{key:"adBlock",getData:function(e){e(function(){var e=document.createElement("div"),t=(e.innerHTML="&nbsp;",e.className="adsbox",false);try{document.body.appendChild(e);t=document.getElementsByClassName("adsbox")[0].offsetHeight===0;document.body.removeChild(e)}catch(e){t=false}return t}())}},{key:"hasLiedLanguages",getData:function(e){e(function(){if(typeof navigator.languages!=="undefined")try{var e=navigator.languages[0].substr(0,2);if(e!==navigator.language.substr(0,2))return true}catch(e){return true}return false}())}},{key:"hasLiedResolution",getData:function(e){e(window.screen.width<window.screen.availWidth||window.screen.height<window.screen.availHeight)}},{key:"hasLiedOs",getData:function(e){e(function(){var e=navigator.userAgent.toLowerCase(),t=navigator.oscpu,n=navigator.platform.toLowerCase(),i,r;if(e.indexOf("windows phone")>=0)i="Windows Phone";else if(e.indexOf("windows")>=0||e.indexOf("win16")>=0||e.indexOf("win32")>=0||e.indexOf("win64")>=0||e.indexOf("win95")>=0||e.indexOf("win98")>=0||e.indexOf("winnt")>=0||e.indexOf("wow64")>=0)i="Windows";else if(e.indexOf("android")>=0)i="Android";else if(e.indexOf("linux")>=0||e.indexOf("cros")>=0||e.indexOf("x11")>=0)i="Linux";else if(e.indexOf("iphone")>=0||e.indexOf("ipad")>=0||e.indexOf("ipod")>=0||e.indexOf("crios")>=0||e.indexOf("fxios")>=0)i="iOS";else if(e.indexOf("macintosh")>=0||e.indexOf("mac_powerpc)")>=0)i="Mac";else i="Other";if(("ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0)&&i!=="Windows"&&i!=="Windows Phone"&&i!=="Android"&&i!=="iOS"&&i!=="Other"&&e.indexOf("cros")===-1)return true;if(typeof t!=="undefined"){t=t.toLowerCase();if(t.indexOf("win")>=0&&i!=="Windows"&&i!=="Windows Phone")return true;else if(t.indexOf("linux")>=0&&i!=="Linux"&&i!=="Android")return true;else if(t.indexOf("mac")>=0&&i!=="Mac"&&i!=="iOS")return true;else if((t.indexOf("win")===-1&&t.indexOf("linux")===-1&&t.indexOf("mac")===-1)!==(i==="Other"))return true}if(n.indexOf("win")>=0&&i!=="Windows"&&i!=="Windows Phone")return true;else if((n.indexOf("linux")>=0||n.indexOf("android")>=0||n.indexOf("pike")>=0)&&i!=="Linux"&&i!=="Android")return true;else if((n.indexOf("mac")>=0||n.indexOf("ipad")>=0||n.indexOf("ipod")>=0||n.indexOf("iphone")>=0)&&i!=="Mac"&&i!=="iOS")return true;else if(n.indexOf("arm")>=0&&i==="Windows Phone")return false;else if(n.indexOf("pike")>=0&&e.indexOf("opera mini")>=0)return false;else{var o=n.indexOf("win")<0&&n.indexOf("linux")<0&&n.indexOf("mac")<0&&n.indexOf("iphone")<0&&n.indexOf("ipad")<0&&n.indexOf("ipod")<0;if(o!==(i==="Other"))return true}return typeof navigator.plugins==="undefined"&&i!=="Windows"&&i!=="Windows Phone"}())}},{key:"hasLiedBrowser",getData:function(e){e(function(){var e=navigator.userAgent.toLowerCase(),t=navigator.productSub,n;if(e.indexOf("edge/")>=0||e.indexOf("iemobile/")>=0)return false;else if(e.indexOf("opera mini")>=0)return false;else if(e.indexOf("firefox/")>=0)n="Firefox";else if(e.indexOf("opera/")>=0||e.indexOf(" opr/")>=0)n="Opera";else if(e.indexOf("chrome/")>=0)n="Chrome";else if(e.indexOf("safari/")>=0)if(e.indexOf("android 1.")>=0||e.indexOf("android 2.")>=0||e.indexOf("android 3.")>=0||e.indexOf("android 4.")>=0)n="AOSP";else n="Safari";else if(e.indexOf("trident/")>=0)n="Internet Explorer";else n="Other";if((n==="Chrome"||n==="Safari"||n==="Opera")&&t!=="20030107")return true;var i=eval.toString().length,r;if(i===37&&n!=="Safari"&&n!=="Firefox"&&n!=="Other")return true;else if(i===39&&n!=="Internet Explorer"&&n!=="Other")return true;else if(i===33&&n!=="Chrome"&&n!=="AOSP"&&n!=="Opera"&&n!=="Other")return true;try{throw"a"}catch(e){try{e.toSource();r=true}catch(e){r=false}}return r&&n!=="Firefox"&&n!=="Other"}())}},{key:"touchSupport",getData:function(e){e(function(){var e=0,t,n;if(typeof navigator.maxTouchPoints!=="undefined")e=navigator.maxTouchPoints;else if(typeof navigator.msMaxTouchPoints!=="undefined")e=navigator.msMaxTouchPoints;try{document.createEvent("TouchEvent");t=true}catch(e){t=false}return[e,t,"ontouchstart"in window]}())}},{key:"fonts",getData:function(e,t){var l=["monospace","sans-serif","serif"],u=["Andale Mono","Arial","Arial Black","Arial Hebrew","Arial MT","Arial Narrow","Arial Rounded MT Bold","Arial Unicode MS","Bitstream Vera Sans Mono","Book Antiqua","Bookman Old Style","Calibri","Cambria","Cambria Math","Century","Century Gothic","Century Schoolbook","Comic Sans","Comic Sans MS","Consolas","Courier","Courier New","Geneva","Georgia","Helvetica","Helvetica Neue","Impact","Lucida Bright","Lucida Calligraphy","Lucida Console","Lucida Fax","LUCIDA GRANDE","Lucida Handwriting","Lucida Sans","Lucida Sans Typewriter","Lucida Sans Unicode","Microsoft Sans Serif","Monaco","Monotype Corsiva","MS Gothic","MS Outlook","MS PGothic","MS Reference Sans Serif","MS Sans Serif","MS Serif","MYRIAD","MYRIAD PRO","Palatino","Palatino Linotype","Segoe Print","Segoe Script","Segoe UI","Segoe UI Light","Segoe UI Semibold","Segoe UI Symbol","Tahoma","Times","Times New Roman","Times New Roman PS","Trebuchet MS","Verdana","Wingdings","Wingdings 2","Wingdings 3"],n=(t.fonts.extendedJsFonts&&(u=u.concat(["Abadi MT Condensed Light","Academy Engraved LET","ADOBE CASLON PRO","Adobe Garamond","ADOBE GARAMOND PRO","Agency FB","Aharoni","Albertus Extra Bold","Albertus Medium","Algerian","Amazone BT","American Typewriter","American Typewriter Condensed","AmerType Md BT","Andalus","Angsana New","AngsanaUPC","Antique Olive","Aparajita","Apple Chancery","Apple Color Emoji","Apple SD Gothic Neo","Arabic Typesetting","ARCHER","ARNO PRO","Arrus BT","Aurora Cn BT","AvantGarde Bk BT","AvantGarde Md BT","AVENIR","Ayuthaya","Bandy","Bangla Sangam MN","Bank Gothic","BankGothic Md BT","Baskerville","Baskerville Old Face","Batang","BatangChe","Bauer Bodoni","Bauhaus 93","Bazooka","Bell MT","Bembo","Benguiat Bk BT","Berlin Sans FB","Berlin Sans FB Demi","Bernard MT Condensed","BernhardFashion BT","BernhardMod BT","Big Caslon","BinnerD","Blackadder ITC","BlairMdITC TT","Bodoni 72","Bodoni 72 Oldstyle","Bodoni 72 Smallcaps","Bodoni MT","Bodoni MT Black","Bodoni MT Condensed","Bodoni MT Poster Compressed","Bookshelf Symbol 7","Boulder","Bradley Hand","Bradley Hand ITC","Bremen Bd BT","Britannic Bold","Broadway","Browallia New","BrowalliaUPC","Brush Script MT","Californian FB","Calisto MT","Calligrapher","Candara","CaslonOpnface BT","Castellar","Centaur","Cezanne","CG Omega","CG Times","Chalkboard","Chalkboard SE","Chalkduster","Charlesworth","Charter Bd BT","Charter BT","Chaucer","ChelthmITC Bk BT","Chiller","Clarendon","Clarendon Condensed","CloisterBlack BT","Cochin","Colonna MT","Constantia","Cooper Black","Copperplate","Copperplate Gothic","Copperplate Gothic Bold","Copperplate Gothic Light","CopperplGoth Bd BT","Corbel","Cordia New","CordiaUPC","Cornerstone","Coronet","Cuckoo","Curlz MT","DaunPenh","Dauphin","David","DB LCD Temp","DELICIOUS","Denmark","DFKai-SB","Didot","DilleniaUPC","DIN","DokChampa","Dotum","DotumChe","Ebrima","Edwardian Script ITC","Elephant","English 111 Vivace BT","Engravers MT","EngraversGothic BT","Eras Bold ITC","Eras Demi ITC","Eras Light ITC","Eras Medium ITC","EucrosiaUPC","Euphemia","Euphemia UCAS","EUROSTILE","Exotc350 Bd BT","FangSong","Felix Titling","Fixedsys","FONTIN","Footlight MT Light","Forte","FrankRuehl","Fransiscan","Freefrm721 Blk BT","FreesiaUPC","Freestyle Script","French Script MT","FrnkGothITC Bk BT","Fruitger","FRUTIGER","Futura","Futura Bk BT","Futura Lt BT","Futura Md BT","Futura ZBlk BT","FuturaBlack BT","Gabriola","Galliard BT","Gautami","Geeza Pro","Geometr231 BT","Geometr231 Hv BT","Geometr231 Lt BT","GeoSlab 703 Lt BT","GeoSlab 703 XBd BT","Gigi","Gill Sans","Gill Sans MT","Gill Sans MT Condensed","Gill Sans MT Ext Condensed Bold","Gill Sans Ultra Bold","Gill Sans Ultra Bold Condensed","Gisha","Gloucester MT Extra Condensed","GOTHAM","GOTHAM BOLD","Goudy Old Style","Goudy Stout","GoudyHandtooled BT","GoudyOLSt BT","Gujarati Sangam MN","Gulim","GulimChe","Gungsuh","GungsuhChe","Gurmukhi MN","Haettenschweiler","Harlow Solid Italic","Harrington","Heather","Heiti SC","Heiti TC","HELV","Herald","High Tower Text","Hiragino Kaku Gothic ProN","Hiragino Mincho ProN","Hoefler Text","Humanst 521 Cn BT","Humanst521 BT","Humanst521 Lt BT","Imprint MT Shadow","Incised901 Bd BT","Incised901 BT","Incised901 Lt BT","INCONSOLATA","Informal Roman","Informal011 BT","INTERSTATE","IrisUPC","Iskoola Pota","JasmineUPC","Jazz LET","Jenson","Jester","Jokerman","Juice ITC","Kabel Bk BT","Kabel Ult BT","Kailasa","KaiTi","Kalinga","Kannada Sangam MN","Kartika","Kaufmann Bd BT","Kaufmann BT","Khmer UI","KodchiangUPC","Kokila","Korinna BT","Kristen ITC","Krungthep","Kunstler Script","Lao UI","Latha","Leelawadee","Letter Gothic","Levenim MT","LilyUPC","Lithograph","Lithograph Light","Long Island","Lydian BT","Magneto","Maiandra GD","Malayalam Sangam MN","Malgun Gothic","Mangal","Marigold","Marion","Marker Felt","Market","Marlett","Matisse ITC","Matura MT Script Capitals","Meiryo","Meiryo UI","Microsoft Himalaya","Microsoft JhengHei","Microsoft New Tai Lue","Microsoft PhagsPa","Microsoft Tai Le","Microsoft Uighur","Microsoft YaHei","Microsoft Yi Baiti","MingLiU","MingLiU_HKSCS","MingLiU_HKSCS-ExtB","MingLiU-ExtB","Minion","Minion Pro","Miriam","Miriam Fixed","Mistral","Modern","Modern No. 20","Mona Lisa Solid ITC TT","Mongolian Baiti","MONO","MoolBoran","Mrs Eaves","MS LineDraw","MS Mincho","MS PMincho","MS Reference Specialty","MS UI Gothic","MT Extra","MUSEO","MV Boli","Nadeem","Narkisim","NEVIS","News Gothic","News GothicMT","NewsGoth BT","Niagara Engraved","Niagara Solid","Noteworthy","NSimSun","Nyala","OCR A Extended","Old Century","Old English Text MT","Onyx","Onyx BT","OPTIMA","Oriya Sangam MN","OSAKA","OzHandicraft BT","Palace Script MT","Papyrus","Parchment","Party LET","Pegasus","Perpetua","Perpetua Titling MT","PetitaBold","Pickwick","Plantagenet Cherokee","Playbill","PMingLiU","PMingLiU-ExtB","Poor Richard","Poster","PosterBodoni BT","PRINCETOWN LET","Pristina","PTBarnum BT","Pythagoras","Raavi","Rage Italic","Ravie","Ribbon131 Bd BT","Rockwell","Rockwell Condensed","Rockwell Extra Bold","Rod","Roman","Sakkal Majalla","Santa Fe LET","Savoye LET","Sceptre","Script","Script MT Bold","SCRIPTINA","Serifa","Serifa BT","Serifa Th BT","ShelleyVolante BT","Sherwood","Shonar Bangla","Showcard Gothic","Shruti","Signboard","SILKSCREEN","SimHei","Simplified Arabic","Simplified Arabic Fixed","SimSun","SimSun-ExtB","Sinhala Sangam MN","Sketch Rockwell","Skia","Small Fonts","Snap ITC","Snell Roundhand","Socket","Souvenir Lt BT","Staccato222 BT","Steamer","Stencil","Storybook","Styllo","Subway","Swis721 BlkEx BT","Swiss911 XCm BT","Sylfaen","Synchro LET","System","Tamil Sangam MN","Technical","Teletype","Telugu Sangam MN","Tempus Sans ITC","Terminal","Thonburi","Traditional Arabic","Trajan","TRAJAN PRO","Tristan","Tubular","Tunga","Tw Cen MT","Tw Cen MT Condensed","Tw Cen MT Condensed Extra Bold","TypoUpright BT","Unicorn","Univers","Univers CE 55 Medium","Univers Condensed","Utsaah","Vagabond","Vani","Vijaya","Viner Hand ITC","VisualUI","Vivaldi","Vladimir Script","Vrinda","Westminster","WHITNEY","Wide Latin","ZapfEllipt BT","ZapfHumnst BT","ZapfHumnst Dm BT","Zapfino","Zurich BlkEx BT","Zurich Ex BT","ZWAdobeF"])),u=(u=u.concat(t.fonts.userDefinedFonts)).filter(function(e,t){return u.indexOf(e)===t}),"mmmmmmmmmmlli"),i="72px",t=document.getElementsByTagName("body")[0],r=document.createElement("div"),f=document.createElement("div"),o={},a={},d=function(){var e=document.createElement("span");return e.style.position="absolute",e.style.left="-9999px",e.style.fontSize=i,e.style.fontStyle="normal",e.style.fontWeight="normal",e.style.letterSpacing="normal",e.style.lineBreak="auto",e.style.lineHeight="normal",e.style.textTransform="none",e.style.textAlign="left",e.style.textDecoration="none",e.style.textShadow="none",e.style.whiteSpace="normal",e.style.wordBreak="normal",e.style.wordSpacing="normal",e.innerHTML=n,e},s=function(){for(var e=[],t=0,n=l.length;t<n;t++){var i=d();i.style.fontFamily=l[t],r.appendChild(i),e.push(i)}return e}();t.appendChild(r);for(var c=0,h=l.length;c<h;c++)o[l[c]]=s[c].offsetWidth,a[l[c]]=s[c].offsetHeight;for(var p=function(){for(var e,t,n={},i=0,r=u.length;i<r;i++){for(var o=[],a=0,s=l.length;a<s;a++){c=u[i],e=l[a],t=void 0,(t=d()).style.fontFamily="'"+c+"',"+e;var c=t;f.appendChild(c),o.push(c)}n[u[i]]=o}return n}(),g=(t.appendChild(f),[]),v=0,y=u.length;v<y;v++)!function(e){for(var t=!1,n=0;n<l.length;n++)if(t=e[n].offsetWidth!==o[l[n]]||e[n].offsetHeight!==a[l[n]])return t;return t}(p[u[v]])||g.push(u[v]);t.removeChild(f),t.removeChild(r),e(g)},pauseBefore:!0},{key:"fontsFlash",getData:function(t,e){var n,i,r;return void 0===window.swfobject?t("swf object not loaded"):window.swfobject.hasFlashPlayerVersion("9.0.0")?e.fonts.swfPath?(n=function(e){t(e)},e=e,i="___fp_swf_loaded",window[i]=function(e){n(e)},r=e.fonts.swfContainerId,m(),void window.swfobject.embedSWF(e.fonts.swfPath,r,"1","1","9.0.0",!1,{onReady:i},{allowScriptAccess:"always",menu:"false"},{})):t("missing options.fonts.swfPath"):t("flash not installed")},pauseBefore:!0},{key:"audio",getData:function(n,e){var t,i,r,o,a,s=e.audio;return s.excludeIOS11&&navigator.userAgent.match(/OS 11.+Version\/11.+Safari/)?n(e.EXCLUDED):null==(t=window.OfflineAudioContext||window.webkitOfflineAudioContext)?n(e.NOT_AVAILABLE):(i=new t(1,44100,44100),(r=i.createOscillator()).type="triangle",r.frequency.setValueAtTime(1e4,i.currentTime),o=i.createDynamicsCompressor(),y([["threshold",-50],["knee",40],["ratio",12],["reduction",-20],["attack",0],["release",.25]],function(e){void 0!==o[e[0]]&&"function"==typeof o[e[0]].setValueAtTime&&o[e[0]].setValueAtTime(e[1],i.currentTime)}),r.connect(o),o.connect(i.destination),r.start(0),i.startRendering(),a=setTimeout(function(){return console.warn('Audio fingerprint timed out. Please report bug at https://github.com/fingerprintjs/fingerprintjs with your user agent: "'+navigator.userAgent+'".'),i.oncomplete=function(){},i=null,n("audioTimeout")},s.timeout),void(i.oncomplete=function(e){var t;try{clearTimeout(a),t=e.renderedBuffer.getChannelData(0).slice(4500,5e3).reduce(function(e,t){return e+Math.abs(t)},0).toString(),r.disconnect(),o.disconnect()}catch(e){return void n(e)}n(t)}))}},{key:"enumerateDevices",getData:function(t,e){if(!navigator.mediaDevices||!navigator.mediaDevices.enumerateDevices)return t(e.NOT_AVAILABLE);navigator.mediaDevices.enumerateDevices().then(function(e){t(e.map(function(e){return"id="+e.deviceId+";gid="+e.groupId+";"+e.kind+";"+e.label}))}).catch(function(e){t(e)})}}];return i.get=function(n,i){var e,t,r=n=i?n||{}:(i=n,{}),o=l;if(null!=o)for(t in o)null==(e=o[t])||Object.prototype.hasOwnProperty.call(r,t)||(r[t]=e);n.components=n.extraComponents.concat(_);function a(e){if((c+=1)>=n.components.length)i(s.data);else{var t=n.components[c];if(n.excludes[t.key])a(!1);else if(!e&&t.pauseBefore)--c,setTimeout(function(){a(!0)},1);else try{t.getData(function(e){s.addPreprocessedComponent(t.key,e),a(!1)},n)}catch(e){s.addPreprocessedComponent(t.key,String(e)),a(!1)}}}var s={data:[],addPreprocessedComponent:function(e,t){"function"==typeof n.preprocessor&&(t=n.preprocessor(e,t)),s.data.push({key:e,value:t})}},c=-1;a(!1)},i.getPromise=function(n){return new Promise(function(e,t){i.get(n,e)})},i.getV18=function(o,a){return null==a&&(a=o,o={}),i.get(o,function(e){for(var t=[],n=0;n<e.length;n++){var i=e[n];i.value===(o.NOT_AVAILABLE||"not available")?t.push({key:i.key,value:"unknown"}):"plugins"===i.key?t.push({key:"plugins",value:c(i.value,function(e){var t=c(e[2],function(e){return e.join?e.join("~"):e}).join(",");return[e[0],e[1],t].join("::")})}):-1!==["canvas","webgl"].indexOf(i.key)&&Array.isArray(i.value)?t.push({key:i.key,value:i.value.join("~")}):-1!==["sessionStorage","localStorage","indexedDb","addBehavior","openDatabase"].indexOf(i.key)?i.value&&t.push({key:i.key,value:1}):i.value?t.push(i.value.join?{key:i.key,value:i.value.join(";")}:i):t.push({key:i.key,value:i.value})}var r=s(c(t,function(e){return e.value}).join("~~~"),31);a(r,t)})},i.x64hash128=s,i.VERSION="2.1.5",i})},function(t,e){!function(e){t.exports=e}.call(this,{})},function(e,t,n){e.exports=(e=n(12),n(13),n(14),n(15),n(16),n(17),n(18),n(19),n(20),n(21),n(22),n(23),n(24),n(25),n(26),n(27),n(28),n(29),n(30),n(31),n(32),n(33),n(34),n(35),n(36),n(37),n(38),n(39),n(40),n(41),n(42),n(43),n(44),e)},function(e,t,n){e.exports=(e=function(l){var e={},t=e.lib={},n=t.Base={extend:function(e){c.prototype=this;var t=new c;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),(t.init.prototype=t).$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},u=t.WordArray=n.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||r).stringify(this)},concat:function(e){var t=this.words,n=e.words,i=this.sigBytes,r=e.sigBytes;if(this.clamp(),i%4)for(var o=0;o<r;o++){var a=255&n[o>>>2]>>>24-o%4*8;t[i+o>>>2]|=a<<24-(i+o)%4*8}else if(65535<n.length)for(o=0;o<r;o+=4)t[i+o>>>2]=n[o>>>2];else t.push.apply(t,n);return this.sigBytes+=r,this},clamp:function(){var e=this.words,t=this.sigBytes;e[t>>>2]&=4294967295<<32-t%4*8,e.length=l.ceil(t/4)},clone:function(){var e=n.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(0|4294967296*l.random());return new u.init(t,e)}}),i=e.enc={},r=i.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,i=[],r=0;r<n;r++){var o=255&t[r>>>2]>>>24-r%4*8;i.push((o>>>4).toString(16)),i.push((15&o).toString(16))}return i.join("")},parse:function(e){for(var t=e.length,n=[],i=0;i<t;i+=2)n[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new u.init(n,t/2)}},o=i.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,i=[],r=0;r<n;r++){var o=255&t[r>>>2]>>>24-r%4*8;i.push(String.fromCharCode(o))}return i.join("")},parse:function(e){for(var t=e.length,n=[],i=0;i<t;i++)n[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new u.init(n,t)}},a=i.Utf8={stringify:function(e){try{return decodeURIComponent(escape(o.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return o.parse(unescape(encodeURIComponent(e)))}},s=t.BufferedBlockAlgorithm=n.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=a.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(e){var t=this._data,n=t.words,i=t.sigBytes,r=this.blockSize,o=i/(4*r),a=(e?l.ceil(o):l.max((0|o)-this._minBufferSize,0))*r,e=l.min(4*a,i);if(a){for(var s=0;s<a;s+=r)this._doProcessBlock(n,s);var c=n.splice(0,a);t.sigBytes-=e}return new u.init(c,e)},clone:function(){var e=n.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});function c(){}t.Hasher=s.extend({cfg:n.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){s.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(n){return function(e,t){return new n.init(t).finalize(e)}},_createHmacHelper:function(n){return function(e,t){return new f.HMAC.init(n,t).finalize(e)}}});var f=e.algo={};return e}(Math),e)},function(e,t,n){var r,o;e.exports=(e=n(12),n=(e=e).lib,r=n.Base,o=n.WordArray,(n=e.x64={}).Word=r.extend({init:function(e,t){this.high=e,this.low=t}}),void(n.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,n=[],i=0;i<t;i++){var r=e[i];n.push(r.high),n.push(r.low)}return o.create(n,this.sigBytes)},clone:function(){for(var e=r.clone.call(this),t=e.words=this.words.slice(0),n=t.length,i=0;i<n;i++)t[i]=t[i].clone();return e}})))},function(e,t,n){e.exports=function(e){if("function"==typeof ArrayBuffer){var t=e,n=t.lib,i=n.WordArray,r=i.init,o=i.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,n=[],i=0;t>i;i++)n[i>>>2]|=e[i]<<24-8*(i%4);r.call(this,n,t)}else r.apply(this,arguments)};o.prototype=i}return e.lib.WordArray}(n(12))},function(e,t,n){function a(e){return 4278255360&e<<8|16711935&e>>>8}var r;e.exports=(e=n(12),r=e.lib.WordArray,(n=e.enc).Utf16=n.Utf16BE={stringify:function(e){for(var t=e.words,n=e.sigBytes,i=[],r=0;r<n;r+=2){var o=65535&t[r>>>2]>>>16-r%4*8;i.push(String.fromCharCode(o))}return i.join("")},parse:function(e){for(var t=e.length,n=[],i=0;i<t;i++)n[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return r.create(n,2*t)}},n.Utf16LE={stringify:function(e){for(var t=e.words,n=e.sigBytes,i=[],r=0;r<n;r+=2){var o=a(65535&t[r>>>2]>>>16-r%4*8);i.push(String.fromCharCode(o))}return i.join("")},parse:function(e){for(var t=e.length,n=[],i=0;i<t;i++)n[i>>>1]|=a(e.charCodeAt(i)<<16-i%2*16);return r.create(n,2*t)}},e.enc.Utf16)},function(e,t,n){var l;e.exports=(e=n(12),l=e.lib.WordArray,e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,i=this._map;e.clamp();for(var r=[],o=0;o<n;o+=3)for(var a=(255&t[o>>>2]>>>24-o%4*8)<<16|(255&t[o+1>>>2]>>>24-(o+1)%4*8)<<8|255&t[o+2>>>2]>>>24-(o+2)%4*8,s=0;s<4&&o+.75*s<n;s++)r.push(i.charAt(63&a>>>6*(3-s)));var c=i.charAt(64);if(c)for(;r.length%4;)r.push(c);return r.join("")},parse:function(e){var t=e.length,n=this._map,i=n.charAt(64);i&&-1!=(i=e.indexOf(i))&&(t=i);for(var r,o,a=[],s=0,c=0;c<t;c++)c%4&&(r=n.indexOf(e.charAt(c-1))<<c%4*2,o=n.indexOf(e.charAt(c))>>>6-c%4*2,a[s>>>2]|=(r|o)<<24-s%4*8,s++);return l.create(a,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.enc.Base64)},function(e,t,n){e.exports=function(e){var u=Math;function T(e,t,n,i,r,o,a){var s=e+(t&n|~t&i)+r+a;return(s<<o|s>>>32-o)+t}function O(e,t,n,i,r,o,a){var s=e+(t&i|n&~i)+r+a;return(s<<o|s>>>32-o)+t}function A(e,t,n,i,r,o,a){var s=e+(t^n^i)+r+a;return(s<<o|s>>>32-o)+t}function E(e,t,n,i,r,o,a){var s=e+(n^(t|~i))+r+a;return(s<<o|s>>>32-o)+t}for(var t=e,n=t.lib,i=n.WordArray,r=n.Hasher,o=t.algo,k=[],a=0;64>a;a++)k[a]=0|4294967296*u.abs(u.sin(a+1));var s=o.MD5=r.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;16>n;n++){var i=t+n,r=e[i];e[i]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var o=this._hash.words,a=e[t+0],s=e[t+1],c=e[t+2],l=e[t+3],u=e[t+4],f=e[t+5],d=e[t+6],h=e[t+7],p=e[t+8],g=e[t+9],v=e[t+10],y=e[t+11],m=e[t+12],w=e[t+13],S=e[t+14],_=e[t+15],C=o[0],b=o[1],x=o[2],B=o[3];C=T(C,b,x,B,a,7,k[0]),B=T(B,C,b,x,s,12,k[1]),x=T(x,B,C,b,c,17,k[2]),b=T(b,x,B,C,l,22,k[3]),C=T(C,b,x,B,u,7,k[4]),B=T(B,C,b,x,f,12,k[5]),x=T(x,B,C,b,d,17,k[6]),b=T(b,x,B,C,h,22,k[7]),C=T(C,b,x,B,p,7,k[8]),B=T(B,C,b,x,g,12,k[9]),x=T(x,B,C,b,v,17,k[10]),b=T(b,x,B,C,y,22,k[11]),C=T(C,b,x,B,m,7,k[12]),B=T(B,C,b,x,w,12,k[13]),x=T(x,B,C,b,S,17,k[14]),b=T(b,x,B,C,_,22,k[15]),C=O(C,b,x,B,s,5,k[16]),B=O(B,C,b,x,d,9,k[17]),x=O(x,B,C,b,y,14,k[18]),b=O(b,x,B,C,a,20,k[19]),C=O(C,b,x,B,f,5,k[20]),B=O(B,C,b,x,v,9,k[21]),x=O(x,B,C,b,_,14,k[22]),b=O(b,x,B,C,u,20,k[23]),C=O(C,b,x,B,g,5,k[24]),B=O(B,C,b,x,S,9,k[25]),x=O(x,B,C,b,l,14,k[26]),b=O(b,x,B,C,p,20,k[27]),C=O(C,b,x,B,w,5,k[28]),B=O(B,C,b,x,c,9,k[29]),x=O(x,B,C,b,h,14,k[30]),b=O(b,x,B,C,m,20,k[31]),C=A(C,b,x,B,f,4,k[32]),B=A(B,C,b,x,p,11,k[33]),x=A(x,B,C,b,y,16,k[34]),b=A(b,x,B,C,S,23,k[35]),C=A(C,b,x,B,s,4,k[36]),B=A(B,C,b,x,u,11,k[37]),x=A(x,B,C,b,h,16,k[38]),b=A(b,x,B,C,v,23,k[39]),C=A(C,b,x,B,w,4,k[40]),B=A(B,C,b,x,a,11,k[41]),x=A(x,B,C,b,l,16,k[42]),b=A(b,x,B,C,d,23,k[43]),C=A(C,b,x,B,g,4,k[44]),B=A(B,C,b,x,m,11,k[45]),x=A(x,B,C,b,_,16,k[46]),b=A(b,x,B,C,c,23,k[47]),C=E(C,b,x,B,a,6,k[48]),B=E(B,C,b,x,h,10,k[49]),x=E(x,B,C,b,S,15,k[50]),b=E(b,x,B,C,f,21,k[51]),C=E(C,b,x,B,m,6,k[52]),B=E(B,C,b,x,l,10,k[53]),x=E(x,B,C,b,v,15,k[54]),b=E(b,x,B,C,s,21,k[55]),C=E(C,b,x,B,p,6,k[56]),B=E(B,C,b,x,_,10,k[57]),x=E(x,B,C,b,d,15,k[58]),b=E(b,x,B,C,w,21,k[59]),C=E(C,b,x,B,u,6,k[60]),B=E(B,C,b,x,y,10,k[61]),x=E(x,B,C,b,c,15,k[62]),b=E(b,x,B,C,g,21,k[63]),o[0]=0|o[0]+C,o[1]=0|o[1]+b,o[2]=0|o[2]+x,o[3]=0|o[3]+B},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;t[i>>>5]|=128<<24-i%32;var r=u.floor(n/4294967296),o=n;t[(i+64>>>9<<4)+15]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t[(i+64>>>9<<4)+14]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),e.sigBytes=4*(t.length+1),this._process();for(var a=this._hash,s=a.words,c=0;4>c;c++){var l=s[c];s[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return a},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});return t.MD5=r._createHelper(s),t.HmacMD5=r._createHmacHelper(s),e.MD5}(n(12))},function(e,t,n){var i,r,u,o;e.exports=(e=n(12),o=(n=e).lib,i=o.WordArray,r=o.Hasher,o=n.algo,u=[],o=o.SHA1=r.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,i=n[0],r=n[1],o=n[2],a=n[3],s=n[4],c=0;c<80;c++){c<16?u[c]=0|e[t+c]:(l=u[c-3]^u[c-8]^u[c-14]^u[c-16],u[c]=l<<1|l>>>31);var l=(i<<5|i>>>27)+s+u[c];l+=c<20?1518500249+(r&o|~r&a):c<40?1859775393+(r^o^a):c<60?(r&o|r&a|o&a)-1894007588:(r^o^a)-899497514,s=a,a=o,o=r<<30|r>>>2,r=i,i=l}n[0]=0|n[0]+i,n[1]=0|n[1]+r,n[2]=0|n[2]+o,n[3]=0|n[3]+a,n[4]=0|n[4]+s},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[14+(64+i>>>9<<4)]=Math.floor(n/4294967296),t[15+(64+i>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}}),n.SHA1=r._createHelper(o),n.HmacSHA1=r._createHmacHelper(o),e.SHA1)},function(e,t,n){e.exports=function(e){var r=Math,t=e,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.algo,s=[],C=[];function c(e){for(var t=r.sqrt(e),n=2;t>=n;n++)if(!(e%n))return!1;return!0}function l(e){return 0|4294967296*(e-(0|e))}for(var u=2,f=0;64>f;)c(u)&&(8>f&&(s[f]=l(r.pow(u,.5))),C[f]=l(r.pow(u,1/3)),f++),u++;var b=[],d=a.SHA256=o.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,i=n[0],r=n[1],o=n[2],a=n[3],s=n[4],c=n[5],l=n[6],u=n[7],f=0;64>f;f++){if(16>f)b[f]=0|e[t+f];else{var d=b[f-15],h=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,p=b[f-2],g=(p<<15|p>>>17)^(p<<13|p>>>19)^p>>>10;b[f]=h+b[f-7]+g+b[f-16]}var v=s&c^~s&l,y=i&r^i&o^r&o,m=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),w=(s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25),S=u+w+v+C[f]+b[f],_=m+y;u=l,l=c,c=s,s=0|a+S,a=o,o=r,r=i,i=0|S+_}n[0]=0|n[0]+i,n[1]=0|n[1]+r,n[2]=0|n[2]+o,n[3]=0|n[3]+a,n[4]=0|n[4]+s,n[5]=0|n[5]+c,n[6]=0|n[6]+l,n[7]=0|n[7]+u},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[(i+64>>>9<<4)+14]=r.floor(n/4294967296),t[(i+64>>>9<<4)+15]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});return t.SHA256=o._createHelper(d),t.HmacSHA256=o._createHmacHelper(d),e.SHA256}(n(12))},function(e,t,n){var i,r,o;e.exports=(e=n(12),n(19),i=(n=e).lib.WordArray,o=n.algo,r=o.SHA256,o=o.SHA224=r.extend({_doReset:function(){this._hash=new i.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=r._doFinalize.call(this);return e.sigBytes-=4,e}}),n.SHA224=r._createHelper(o),n.HmacSHA224=r._createHmacHelper(o),e.SHA224)},function(e,t,n){e.exports=function(e){function t(){return a.create.apply(a,arguments)}for(var n=e,i,r=n.lib.Hasher,o=n.x64,a=o.Word,s=o.WordArray,c=n.algo,xe=[t(1116352408,3609767458),t(1899447441,602891725),t(3049323471,3964484399),t(3921009573,2173295548),t(961987163,4081628472),t(1508970993,3053834265),t(2453635748,2937671579),t(2870763221,3664609560),t(3624381080,2734883394),t(310598401,1164996542),t(607225278,1323610764),t(1426881987,3590304994),t(1925078388,4068182383),t(2162078206,991336113),t(2614888103,633803317),t(3248222580,3479774868),t(3835390401,2666613458),t(4022224774,944711139),t(264347078,2341262773),t(604807628,2007800933),t(770255983,1495990901),t(1249150122,1856431235),t(1555081692,3175218132),t(1996064986,2198950837),t(2554220882,3999719339),t(2821834349,766784016),t(2952996808,2566594879),t(3210313671,3203337956),t(3336571891,1034457026),t(3584528711,2466948901),t(113926993,3758326383),t(338241895,168717936),t(666307205,1188179964),t(773529912,1546045734),t(1294757372,1522805485),t(1396182291,2643833823),t(1695183700,2343527390),t(1986661051,1014477480),t(2177026350,1206759142),t(2456956037,344077627),t(2730485921,1290863460),t(2820302411,3158454273),t(3259730800,3505952657),t(3345764771,106217008),t(3516065817,3606008344),t(3600352804,1432725776),t(4094571909,1467031594),t(275423344,851169720),t(430227734,3100823752),t(506948616,1363258195),t(659060556,3750685593),t(883997877,3785050280),t(958139571,3318307427),t(1322822218,3812723403),t(1537002063,2003034995),t(1747873779,3602036899),t(1955562222,1575990012),t(2024104815,1125592928),t(2227730452,2716904306),t(2361852424,442776044),t(2428436474,593698344),t(2756734187,3733110249),t(3204031479,2999351573),t(3329325298,3815920427),t(3391569614,3928383900),t(3515267271,566280711),t(3940187606,3454069534),t(4118630271,4000239992),t(116418474,1914138554),t(174292421,2731055270),t(289380356,3203993006),t(460393269,320620315),t(685471733,587496836),t(852142971,1086792851),t(1017036298,365543100),t(1126000580,2618297676),t(1288033470,3409855158),t(1501505948,4234509866),t(1607167915,987167468),t(1816402316,1246189591)],Be=[],l=0;80>l;l++)Be[l]=t();var u=c.SHA512=r.extend({_doReset:function(){this._hash=new s.init([new a.init(1779033703,4089235720),new a.init(3144134277,2227873595),new a.init(1013904242,4271175723),new a.init(2773480762,1595750129),new a.init(1359893119,2917565137),new a.init(2600822924,725511199),new a.init(528734635,4215389547),new a.init(1541459225,327033209)])},_doProcessBlock:function(L,D){for(var e=this._hash.words,t=e[0],n=e[1],i=e[2],r=e[3],o=e[4],a=e[5],s=e[6],c=e[7],N=t.high,l=t.low,H=n.high,u=n.low,G=i.high,f=i.low,U=r.high,d=r.low,W=o.high,h=o.low,j=a.high,p=a.low,z=s.high,g=s.low,V=c.high,X=c.low,v=N,y=l,m=H,w=u,S=G,_=f,K=U,C=d,b=W,x=h,J=j,B=p,q=z,T=g,$=V,O=X,A=0;80>A;A++){var Z=Be[A];if(16>A)var Q=Z.high=0|L[D+2*A],E=Z.low=0|L[D+2*A+1];else{var Y=Be[A-15],k=Y.high,M=Y.low,ee=(k>>>1|M<<31)^(k>>>8|M<<24)^k>>>7,te=(M>>>1|k<<31)^(M>>>8|k<<24)^(M>>>7|k<<25),ne=Be[A-2],P=ne.high,F=ne.low,ie=(P>>>19|F<<13)^(P<<3|F>>>29)^P>>>6,re=(F>>>19|P<<13)^(F<<3|P>>>29)^(F>>>6|P<<26),oe=Be[A-7],ae=oe.high,se=oe.low,ce=Be[A-16],le=ce.high,ue=ce.low,E=te+se,Q=ee+ae+(te>>>0>E>>>0?1:0),E=E+re,Q=Q+ie+(re>>>0>E>>>0?1:0),E=E+ue,Q=Q+le+(ue>>>0>E>>>0?1:0);Z.high=Q,Z.low=E}var fe=b&J^~b&q,de=x&B^~x&T,he=v&m^v&S^m&S,pe=y&w^y&_^w&_,ge=(v>>>28|y<<4)^(v<<30|y>>>2)^(v<<25|y>>>7),ve=(y>>>28|v<<4)^(y<<30|v>>>2)^(y<<25|v>>>7),ye=(b>>>14|x<<18)^(b>>>18|x<<14)^(b<<23|x>>>9),me=(x>>>14|b<<18)^(x>>>18|b<<14)^(x<<23|b>>>9),we=xe[A],Se=we.high,_e=we.low,R=O+me,I=$+ye+(O>>>0>R>>>0?1:0),R=R+de,I=I+fe+(de>>>0>R>>>0?1:0),R=R+_e,I=I+Se+(_e>>>0>R>>>0?1:0),R=R+E,I=I+Q+(E>>>0>R>>>0?1:0),Ce=ve+pe,be=ge+he+(ve>>>0>Ce>>>0?1:0);$=q,O=T,q=J,T=B,J=b,B=x,x=0|C+R,b=0|K+I+(C>>>0>x>>>0?1:0),K=S,C=_,S=m,_=w,m=v,w=y,y=0|R+Ce,v=0|I+be+(R>>>0>y>>>0?1:0)}l=t.low=l+y,t.high=N+v+(y>>>0>l>>>0?1:0),u=n.low=u+w,n.high=H+m+(w>>>0>u>>>0?1:0),f=i.low=f+_,i.high=G+S+(_>>>0>f>>>0?1:0),d=r.low=d+C,r.high=U+K+(C>>>0>d>>>0?1:0),h=o.low=h+x,o.high=W+b+(x>>>0>h>>>0?1:0),p=a.low=p+B,a.high=j+J+(B>>>0>p>>>0?1:0),g=s.low=g+T,s.high=z+q+(T>>>0>g>>>0?1:0),X=c.low=X+O,c.high=V+$+(O>>>0>X>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;t[i>>>5]|=128<<24-i%32,t[(i+128>>>10<<5)+30]=Math.floor(n/4294967296),t[(i+128>>>10<<5)+31]=n,e.sigBytes=4*t.length,this._process();var r=this._hash.toX32();return r},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});return n.SHA512=r._createHelper(u),n.HmacSHA512=r._createHmacHelper(u),e.SHA512}(n(12),n(13))},function(e,t,n){var i,r,o,a;e.exports=(e=n(12),n(13),n(21),a=(n=e).x64,i=a.Word,r=a.WordArray,a=n.algo,o=a.SHA512,a=a.SHA384=o.extend({_doReset:function(){this._hash=new r.init([new i.init(3418070365,3238371032),new i.init(1654270250,914150663),new i.init(2438529370,812702999),new i.init(355462360,4144912697),new i.init(1731405415,4290775857),new i.init(2394180231,1750603025),new i.init(3675008525,1694076839),new i.init(1203062813,3204075428)])},_doFinalize:function(){var e=o._doFinalize.call(this);return e.sigBytes-=16,e}}),n.SHA384=o._createHelper(a),n.HmacSHA384=o._createHmacHelper(a),e.SHA384)},function(e,t,n){e.exports=function(e){for(var d=Math,t=e,n=t.lib,h=n.WordArray,i=n.Hasher,r,o=t.x64.Word,a=t.algo,E=[],k=[],M=[],s=1,c=0,l=0;24>l;l++){E[s+5*c]=(l+1)*(l+2)/2%64;var u=c%5,f=(2*s+3*c)%5;s=u,c=f}for(var s=0;5>s;s++)for(var c=0;5>c;c++)k[s+5*c]=c+5*((2*s+3*c)%5);for(var p=1,g=0;24>g;g++){for(var v=0,y=0,m=0;7>m;m++){if(1&p){var w=(1<<m)-1;32>w?y^=1<<w:v^=1<<w-32}128&p?p=113^p<<1:p<<=1}M[g]=o.create(v,y)}for(var P=[],S=0;25>S;S++)P[S]=o.create();var _=a.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;25>t;t++)e[t]=new o.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var n=this._state,i=this.blockSize/2,r=0;i>r;r++){var o=e[t+2*r],a=e[t+2*r+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8);var s=n[r];s.high^=a,s.low^=o}for(var c=0;24>c;c++){for(var l=0;5>l;l++){for(var u=0,f=0,d=0;5>d;d++){var s=n[l+5*d];u^=s.high,f^=s.low}var h=P[l];h.high=u,h.low=f}for(var l=0;5>l;l++)for(var p=P[(l+4)%5],g=P[(l+1)%5],v=g.high,y=g.low,u=p.high^(v<<1|y>>>31),f=p.low^(y<<1|v>>>31),d=0;5>d;d++){var s=n[l+5*d];s.high^=u,s.low^=f}for(var m=1;25>m;m++){var s=n[m],w=s.high,S=s.low,_=E[m];if(32>_)var u=w<<_|S>>>32-_,f=S<<_|w>>>32-_;else var u=S<<_-32|w>>>64-_,f=w<<_-32|S>>>64-_;var C=P[k[m]];C.high=u,C.low=f}var b=P[0],x=n[0];b.high=x.high,b.low=x.low;for(var l=0;5>l;l++)for(var d=0;5>d;d++){var m=l+5*d,s=n[m],B=P[m],T=P[(l+1)%5+5*d],O=P[(l+2)%5+5*d];s.high=B.high^~T.high&O.high,s.low=B.low^~T.low&O.low}var s=n[0],A=M[c];s.high^=A.high,s.low^=A.low}},_doFinalize:function(){var e=this._data,t=e.words;8*this._nDataBytes;var n=8*e.sigBytes,i=32*this.blockSize;t[n>>>5]|=1<<24-n%32,t[(d.ceil((n+1)/i)*i>>>5)-1]|=128,e.sigBytes=4*t.length,this._process();for(var r=this._state,o=this.cfg.outputLength/8,a=o/8,s=[],c=0;a>c;c++){var l=r[c],u=l.high,f=l.low;u=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8),f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),s.push(f),s.push(u)}return new h.init(s,o)},clone:function(){for(var e=i.clone.call(this),t=e._state=this._state.slice(0),n=0;25>n;n++)t[n]=t[n].clone();return e}});return t.SHA3=i._createHelper(_),t.HmacSHA3=i._createHmacHelper(_),e.SHA3}(n(12),n(13))},function(e,t,n){function b(e,t,n){return e&t|~e&n}function x(e,t,n){return e&n|t&~n}function B(e,t){return e<<t|e>>>32-t}var i,r,T,O,A,E,k,M,o;e.exports=(e=n(12),Math,o=(n=e).lib,i=o.WordArray,r=o.Hasher,o=n.algo,T=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),O=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),A=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),E=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),k=i.create([0,1518500249,1859775393,2400959708,2840853838]),M=i.create([1352829926,1548603684,1836072691,2053994217,0]),o=o.RIPEMD160=r.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var i=t+n,r=e[i];e[i]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}for(var o,a,s,c,l,u,f=this._hash.words,d=k.words,h=M.words,p=T.words,g=O.words,v=A.words,y=E.words,m=o=f[0],w=a=f[1],S=s=f[2],_=c=f[3],C=l=f[4],n=0;n<80;n+=1)u=0|(u=B(u=(u=0|o+e[t+p[n]])+(n<16?(a^s^c)+d[0]:n<32?b(a,s,c)+d[1]:n<48?((a|~s)^c)+d[2]:n<64?x(a,s,c)+d[3]:(a^(s|~c))+d[4])|0,v[n]))+l,o=l,l=c,c=B(s,10),s=a,a=u,u=0|(u=B(u=(u=0|m+e[t+g[n]])+(n<16?(w^(S|~_))+h[0]:n<32?x(w,S,_)+h[1]:n<48?((w|~S)^_)+h[2]:n<64?b(w,S,_)+h[3]:(w^S^_)+h[4])|0,y[n]))+C,m=C,C=_,_=B(S,10),S=w,w=u;u=0|f[1]+s+_,f[1]=0|f[2]+c+C,f[2]=0|f[3]+l+m,f[3]=0|f[4]+o+w,f[4]=0|f[0]+a+S,f[0]=u},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;t[i>>>5]|=128<<24-i%32,t[14+(64+i>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(t.length+1),this._process();for(var i=this._hash,r=i.words,o=0;o<5;o++){var a=r[o];r[o]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return i},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}}),n.RIPEMD160=r._createHelper(o),n.HmacRIPEMD160=r._createHmacHelper(o),e.RIPEMD160)},function(e,t,n){var s;e.exports=(e=n(12),n=e.lib.Base,s=e.enc.Utf8,void(e.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=s.parse(t));var n=e.blockSize,i=4*n;(t=t.sigBytes>i?e.finalize(t):t).clamp();for(var e=this._oKey=t.clone(),t=this._iKey=t.clone(),r=e.words,o=t.words,a=0;a<n;a++)r[a]^=1549556828,o[a]^=909522486;e.sigBytes=t.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,e=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(e))}})))},function(e,t,n){var i,y,r,o,m,a;e.exports=(e=n(12),n(18),n(25),r=(n=e).lib,i=r.Base,y=r.WordArray,r=n.algo,o=r.SHA1,m=r.HMAC,a=r.PBKDF2=i.extend({cfg:i.extend({keySize:4,hasher:o,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,i=m.create(n.hasher,e),r=y.create(),o=y.create([1]),a=r.words,s=o.words,c=n.keySize,l=n.iterations;c>a.length;){var u=i.update(t).finalize(o);i.reset();for(var f=u.words,d=f.length,h=u,p=1;p<l;p++){h=i.finalize(h),i.reset();for(var g=h.words,v=0;v<d;v++)f[v]^=g[v]}r.concat(u),s[0]++}return r.sigBytes=4*c,r}}),n.PBKDF2=function(e,t,n){return a.create(n).compute(e,t)},e.PBKDF2)},function(e,t,n){var i,u,r,o,a;e.exports=(e=n(12),n(18),n(25),r=(n=e).lib,i=r.Base,u=r.WordArray,r=n.algo,o=r.MD5,a=r.EvpKDF=i.extend({cfg:i.extend({keySize:4,hasher:o,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,i=n.hasher.create(),r=u.create(),o=r.words,a=n.keySize,s=n.iterations;a>o.length;){c&&i.update(c);var c=i.update(e).finalize(t);i.reset();for(var l=1;l<s;l++)c=i.finalize(c),i.reset();r.concat(c)}return r.sigBytes=4*a,r}}),n.EvpKDF=function(e,t,n){return a.create(n).compute(e,t)},e.EvpKDF)},function(e,t,n){var i,a,r,o,s,c,l,u,f,d,h,p;e.exports=(e=n(12),void(e.lib.Cipher||(n=(e=e).lib,i=n.Base,a=n.WordArray,r=n.BufferedBlockAlgorithm,(f=e.enc).Utf8,o=f.Base64,s=e.algo.EvpKDF,c=n.Cipher=r.extend({cfg:i.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){r.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function r(e){return"string"==typeof e?p:d}return function(i){return{encrypt:function(e,t,n){return r(t).encrypt(i,e,t,n)},decrypt:function(e,t,n){return r(t).decrypt(i,e,t,n)}}}}()}),n.StreamCipher=c.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),f=e.mode={},l=n.BlockCipherMode=i.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),f=f.CBC=function(){function o(e,t,n){var i,r=this._iv;r?(i=r,this._iv=void 0):i=this._prevBlock;for(var o=0;o<n;o++)e[t+o]^=i[o]}var e=l.extend();return e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize;o.call(this,e,t,i),n.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}}),e.Decryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,r=e.slice(t,t+i);n.decryptBlock(e,t),o.call(this,e,t,i),this._prevBlock=r}}),e}(),h=(e.pad={}).Pkcs7={pad:function(e,t){for(var t=4*t,n=t-e.sigBytes%t,i=n<<24|n<<16|n<<8|n,r=[],o=0;o<n;o+=4)r.push(i);t=a.create(r,n);e.concat(t)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},n.BlockCipher=c.extend({cfg:c.cfg.extend({mode:f,padding:h}),reset:function(){c.reset.call(this);var e,t=this.cfg,n=t.iv,t=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=t.createEncryptor:(e=t.createDecryptor,this._minBufferSize=1),this._mode=e.call(t,this,n&&n.words)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),u=n.CipherParams=i.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),f=(e.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,e=e.salt;return(e?a.create([1398893684,1701076831]).concat(e).concat(t):t).toString(o)},parse:function(e){var t,e=o.parse(e),n=e.words;return 1398893684==n[0]&&1701076831==n[1]&&(t=a.create(n.slice(2,4)),n.splice(0,4),e.sigBytes-=16),u.create({ciphertext:e,salt:t})}},d=n.SerializableCipher=i.extend({cfg:i.extend({format:f}),encrypt:function(e,t,n,i){i=this.cfg.extend(i);var r=e.createEncryptor(n,i),t=r.finalize(t),r=r.cfg;return u.create({ciphertext:t,key:n,iv:r.iv,algorithm:e,mode:r.mode,padding:r.padding,blockSize:e.blockSize,formatter:i.format})},decrypt:function(e,t,n,i){return i=this.cfg.extend(i),t=this._parse(t,i.format),e.createDecryptor(n,i).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),h=(e.kdf={}).OpenSSL={execute:function(e,t,n,i){i=i||a.random(8);e=s.create({keySize:t+n}).compute(e,i),n=a.create(e.words.slice(t),4*n);return e.sigBytes=4*t,u.create({key:e,iv:n,salt:i})}},p=n.PasswordBasedCipher=d.extend({cfg:d.cfg.extend({kdf:h}),encrypt:function(e,t,n,i){n=(i=this.cfg.extend(i)).kdf.execute(n,e.keySize,e.ivSize),i.iv=n.iv,e=d.encrypt.call(this,e,t,n.key,i);return e.mixIn(n),e},decrypt:function(e,t,n,i){i=this.cfg.extend(i),t=this._parse(t,i.format);n=i.kdf.execute(n,e.keySize,e.ivSize,t.salt);return i.iv=n.iv,d.decrypt.call(this,e,t,n.key,i)}}))))},function(e,t,n){var i;e.exports=(i=n(12),n(28),i.mode.CFB=function(){function o(e,t,n,i){var r,o=this._iv;o?(r=o.slice(0),this._iv=void 0):r=this._prevBlock,i.encryptBlock(r,0);for(var a=0;a<n;a++)e[t+a]^=r[a]}var e=i.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize;o.call(this,e,t,i,n),this._prevBlock=e.slice(t,t+i)}}),e.Decryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,r=e.slice(t,t+i);o.call(this,e,t,i,n),this._prevBlock=r}}),e}(),i.mode.CFB)},function(e,t,n){var i;e.exports=(i=n(12),n(28),i.mode.CTR=function(){var e=i.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,r=this._iv,o=this._counter,a=(r&&(o=this._counter=r.slice(0),this._iv=void 0),o.slice(0));n.encryptBlock(a,0),o[i-1]=0|o[i-1]+1;for(var s=0;s<i;s++)e[t+s]^=a[s]}});return e.Decryptor=t,e}(),i.mode.CTR)},function(e,t,n){var i;e.exports=(i=n(12),n(28),i.mode.CTRGladman=function(){function c(e){var t,n,i;return 255==(255&e>>24)?(n=255&e>>8,i=255&e,255===(t=255&e>>16)?(t=0,255===n?(n=0,255===i?i=0:++i):++n):++t,e=0,e=(e+=t<<16)+(n<<8)+i):e+=1<<24,e}var e=i.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,r=this._iv,o=this._counter,a=(r&&(o=this._counter=r.slice(0),this._iv=void 0),0===((r=o)[0]=c(r[0]))&&(r[1]=c(r[1])),o.slice(0));n.encryptBlock(a,0);for(var s=0;s<i;s++)e[t+s]^=a[s]}});return e.Decryptor=t,e}(),i.mode.CTRGladman)},function(e,t,n){var i;e.exports=(i=n(12),n(28),i.mode.OFB=function(){var e=i.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,r=this._iv,o=this._keystream;r&&(o=this._keystream=r.slice(0),this._iv=void 0),n.encryptBlock(o,0);for(var a=0;a<i;a++)e[t+a]^=o[a]}});return e.Decryptor=t,e}(),i.mode.OFB)},function(e,t,n){var i;e.exports=(i=n(12),n(28),i.mode.ECB=function(){var e=i.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),e.Decryptor=e.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),e}(),i.mode.ECB)},function(e,t,n){e.exports=(e=n(12),n(28),e.pad.AnsiX923={pad:function(e,t){var n=e.sigBytes,t=4*t,t=t-n%t,n=n+t-1;e.clamp(),e.words[n>>>2]|=t<<24-n%4*8,e.sigBytes+=t},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Ansix923)},function(e,t,n){var i;e.exports=(i=n(12),n(28),i.pad.Iso10126={pad:function(e,t){t*=4,t-=e.sigBytes%t;e.concat(i.lib.WordArray.random(t-1)).concat(i.lib.WordArray.create([t<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},i.pad.Iso10126)},function(e,t,n){var i;e.exports=(i=n(12),n(28),i.pad.Iso97971={pad:function(e,t){e.concat(i.lib.WordArray.create([2147483648],1)),i.pad.ZeroPadding.pad(e,t)},unpad:function(e){i.pad.ZeroPadding.unpad(e),e.sigBytes--}},i.pad.Iso97971)},function(e,t,n){e.exports=(e=n(12),n(28),e.pad.ZeroPadding={pad:function(e,t){t*=4;e.clamp(),e.sigBytes+=t-(e.sigBytes%t||t)},unpad:function(e){for(var t=e.words,n=e.sigBytes-1;!(255&t[n>>>2]>>>24-n%4*8);)n--;e.sigBytes=n+1}},e.pad.ZeroPadding)},function(e,t,n){e.exports=(e=n(12),n(28),e.pad.NoPadding={pad:function(){},unpad:function(){}},e.pad.NoPadding)},function(e,t,n){var i,r;e.exports=(e=n(12),n(28),i=e.lib.CipherParams,r=e.enc.Hex,e.format.Hex={stringify:function(e){return e.ciphertext.toString(r)},parse:function(e){e=r.parse(e);return i.create({ciphertext:e})}},e.format.Hex)},function(e,t,n){e.exports=function(e){for(var t=e,n,i=t.lib.BlockCipher,r=t.algo,u=[],o=[],a=[],s=[],c=[],l=[],f=[],d=[],h=[],p=[],g=[],v=0;256>v;v++)g[v]=128>v?v<<1:283^v<<1;for(var y=0,m=0,v=0;256>v;v++){var w=m^m<<1^m<<2^m<<3^m<<4;w=99^(w>>>8^255&w),u[y]=w,o[w]=y;var S=g[y],_=g[S],C=g[_],b=257*g[w]^16843008*w;a[y]=b<<24|b>>>8,s[y]=b<<16|b>>>16,c[y]=b<<8|b>>>24,l[y]=b;var b=16843009*C^65537*_^257*S^16843008*y;f[w]=b<<24|b>>>8,d[w]=b<<16|b>>>16,h[w]=b<<8|b>>>24,p[w]=b,y?(y=S^g[g[g[C^S]]],m^=g[g[m]]):y=m=1}var x=[0,1,2,4,8,16,32,64,128,27,54],B=r.AES=i.extend({_doReset:function(){for(var e=this._key,t=e.words,n=e.sigBytes/4,i=this._nRounds=n+6,r=4*(i+1),o=this._keySchedule=[],a=0;r>a;a++)if(n>a)o[a]=t[a];else{var s=o[a-1];a%n?n>6&&4==a%n&&(s=u[s>>>24]<<24|u[255&s>>>16]<<16|u[255&s>>>8]<<8|u[255&s]):(s=s<<8|s>>>24,s=u[s>>>24]<<24|u[255&s>>>16]<<16|u[255&s>>>8]<<8|u[255&s],s^=x[0|a/n]<<24),o[a]=o[a-n]^s}for(var c=this._invKeySchedule=[],l=0;r>l;l++){var a=r-l;if(l%4)var s=o[a];else var s=o[a-4];c[l]=4>l||4>=a?s:f[u[s>>>24]]^d[u[255&s>>>16]]^h[u[255&s>>>8]]^p[u[255&s]]}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,s,c,l,u)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,f,d,h,p,o);var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,i,r,o,a,s){for(var c=this._nRounds,l=e[t]^n[0],u=e[t+1]^n[1],f=e[t+2]^n[2],d=e[t+3]^n[3],h=4,p=1;c>p;p++){var g=i[l>>>24]^r[255&u>>>16]^o[255&f>>>8]^a[255&d]^n[h++],v=i[u>>>24]^r[255&f>>>16]^o[255&d>>>8]^a[255&l]^n[h++],y=i[f>>>24]^r[255&d>>>16]^o[255&l>>>8]^a[255&u]^n[h++],m=i[d>>>24]^r[255&l>>>16]^o[255&u>>>8]^a[255&f]^n[h++];l=g,u=v,f=y,d=m}var g=(s[l>>>24]<<24|s[255&u>>>16]<<16|s[255&f>>>8]<<8|s[255&d])^n[h++],v=(s[u>>>24]<<24|s[255&f>>>16]<<16|s[255&d>>>8]<<8|s[255&l])^n[h++],y=(s[f>>>24]<<24|s[255&d>>>16]<<16|s[255&l>>>8]<<8|s[255&u])^n[h++],m=(s[d>>>24]<<24|s[255&l>>>16]<<16|s[255&u>>>8]<<8|s[255&f])^n[h++];e[t]=g,e[t+1]=v,e[t+2]=y,e[t+3]=m},keySize:8});return t.AES=i._createHelper(B),e.AES}(n(12),(n(16),n(17),n(27),n(28)))},function(e,t,n){function u(e,t){t=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=t,this._lBlock^=t<<e}function f(e,t){t=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=t,this._rBlock^=t<<e}var i,r,l,d,h,p,g,o,a;e.exports=(e=n(12),n(16),n(17),n(27),n(28),r=(n=e).lib,i=r.WordArray,r=r.BlockCipher,a=n.algo,l=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],d=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],h=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],p=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],g=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],o=a.DES=r.extend({_doReset:function(){for(var e=this._key.words,t=[],n=0;n<56;n++){var i=l[n]-1;t[n]=1&e[i>>>5]>>>31-i%32}for(var r=this._subKeys=[],o=0;o<16;o++){for(var a=r[o]=[],s=h[o],n=0;n<24;n++)a[0|n/6]|=t[(d[n]-1+s)%28]<<31-n%6,a[4+(0|n/6)]|=t[28+(d[n+24]-1+s)%28]<<31-n%6;a[0]=a[0]<<1|a[0]>>>31;for(n=1;n<7;n++)a[n]=a[n]>>>4*(n-1)+3;a[7]=a[7]<<5|a[7]>>>27}for(var c=this._invSubKeys=[],n=0;n<16;n++)c[n]=r[15-n]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,n){this._lBlock=e[t],this._rBlock=e[t+1],u.call(this,4,252645135),u.call(this,16,65535),f.call(this,2,858993459),f.call(this,8,16711935),u.call(this,1,1431655765);for(var i=0;i<16;i++){for(var r=n[i],o=this._lBlock,a=this._rBlock,s=0,c=0;c<8;c++)s|=p[c][((a^r[c])&g[c])>>>0];this._lBlock=a,this._rBlock=o^s}var l=this._lBlock;this._lBlock=this._rBlock,this._rBlock=l,u.call(this,1,1431655765),f.call(this,8,16711935),f.call(this,2,858993459),u.call(this,16,65535),u.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2}),n.DES=r._createHelper(o),a=a.TripleDES=r.extend({_doReset:function(){var e=this._key.words;this._des1=o.createEncryptor(i.create(e.slice(0,2))),this._des2=o.createEncryptor(i.create(e.slice(2,4))),this._des3=o.createEncryptor(i.create(e.slice(4,6)))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2}),n.TripleDES=r._createHelper(a),e.TripleDES)},function(e,t,n){function i(){for(var e=this._S,t=this._i,n=this._j,i=0,r=0;r<4;r++){var n=(n+e[t=(t+1)%256])%256,o=e[t];e[t]=e[n],e[n]=o,i|=e[(e[t]+e[n])%256]<<24-8*r}return this._i=t,this._j=n,i}var r,o,a;e.exports=(e=n(12),n(16),n(17),n(27),n(28),r=(n=e).lib.StreamCipher,a=n.algo,o=a.RC4=r.extend({_doReset:function(){for(var e=this._key,t=e.words,n=e.sigBytes,i=this._S=[],r=0;r<256;r++)i[r]=r;for(var r=0,o=0;r<256;r++){var a=r%n,a=255&t[a>>>2]>>>24-a%4*8,o=(o+i[r]+a)%256,a=i[r];i[r]=i[o],i[o]=a}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=i.call(this)},keySize:8,ivSize:0}),n.RC4=r._createHelper(o),a=a.RC4Drop=o.extend({cfg:o.cfg.extend({drop:192}),_doReset:function(){o._doReset.call(this);for(var e=this.cfg.drop;0<e;e--)i.call(this)}}),n.RC4Drop=r._createHelper(a),e.RC4)},function(e,t,n){function c(){for(var e=this._X,t=this._C,n=0;n<8;n++)a[n]=t[n];t[0]=0|t[0]+1295307597+this._b,t[1]=0|t[1]+3545052371+(t[0]>>>0<a[0]>>>0?1:0),t[2]=0|t[2]+886263092+(t[1]>>>0<a[1]>>>0?1:0),t[3]=0|t[3]+1295307597+(t[2]>>>0<a[2]>>>0?1:0),t[4]=0|t[4]+3545052371+(t[3]>>>0<a[3]>>>0?1:0),t[5]=0|t[5]+886263092+(t[4]>>>0<a[4]>>>0?1:0),t[6]=0|t[6]+1295307597+(t[5]>>>0<a[5]>>>0?1:0),t[7]=0|t[7]+3545052371+(t[6]>>>0<a[6]>>>0?1:0),this._b=t[7]>>>0<a[7]>>>0?1:0;for(n=0;n<8;n++){var i=e[n]+t[n],r=65535&i,o=i>>>16;s[n]=((r*r>>>17)+r*o>>>15)+o*o^(0|(4294901760&i)*i)+(0|(65535&i)*i)}e[0]=0|s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16),e[1]=0|s[1]+(s[0]<<8|s[0]>>>24)+s[7],e[2]=0|s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16),e[3]=0|s[3]+(s[2]<<8|s[2]>>>24)+s[1],e[4]=0|s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16),e[5]=0|s[5]+(s[4]<<8|s[4]>>>24)+s[3],e[6]=0|s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16),e[7]=0|s[7]+(s[6]<<8|s[6]>>>24)+s[5]}var i,r,a,s,o;e.exports=(e=n(12),n(16),n(17),n(27),n(28),i=(n=e).lib.StreamCipher,o=n.algo,r=[],a=[],s=[],o=o.Rabbit=i.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,n=0;n<4;n++)e[n]=16711935&(e[n]<<8|e[n]>>>24)|4278255360&(e[n]<<24|e[n]>>>8);for(var i=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]],n=this._b=0;n<4;n++)c.call(this);for(n=0;n<8;n++)r[n]^=i[7&n+4];if(t){var t=t.words,o=t[0],t=t[1],o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t=16711935&(t<<8|t>>>24)|4278255360&(t<<24|t>>>8),a=o>>>16|4294901760&t,s=t<<16|65535&o;r[0]^=o,r[1]^=a,r[2]^=t,r[3]^=s,r[4]^=o,r[5]^=a,r[6]^=t,r[7]^=s;for(n=0;n<4;n++)c.call(this)}},_doProcessBlock:function(e,t){var n=this._X;c.call(this),r[0]=n[0]^n[5]>>>16^n[3]<<16,r[1]=n[2]^n[7]>>>16^n[5]<<16,r[2]=n[4]^n[1]>>>16^n[7]<<16,r[3]=n[6]^n[3]>>>16^n[1]<<16;for(var i=0;i<4;i++)r[i]=16711935&(r[i]<<8|r[i]>>>24)|4278255360&(r[i]<<24|r[i]>>>8),e[t+i]^=r[i]},blockSize:4,ivSize:2}),n.Rabbit=i._createHelper(o),e.Rabbit)},function(e,t,n){function s(){for(var e=this._X,t=this._C,n=0;n<8;n++)a[n]=t[n];t[0]=0|t[0]+1295307597+this._b,t[1]=0|t[1]+3545052371+(t[0]>>>0<a[0]>>>0?1:0),t[2]=0|t[2]+886263092+(t[1]>>>0<a[1]>>>0?1:0),t[3]=0|t[3]+1295307597+(t[2]>>>0<a[2]>>>0?1:0),t[4]=0|t[4]+3545052371+(t[3]>>>0<a[3]>>>0?1:0),t[5]=0|t[5]+886263092+(t[4]>>>0<a[4]>>>0?1:0),t[6]=0|t[6]+1295307597+(t[5]>>>0<a[5]>>>0?1:0),t[7]=0|t[7]+3545052371+(t[6]>>>0<a[6]>>>0?1:0),this._b=t[7]>>>0<a[7]>>>0?1:0;for(n=0;n<8;n++){var i=e[n]+t[n],r=65535&i,o=i>>>16;c[n]=((r*r>>>17)+r*o>>>15)+o*o^(0|(4294901760&i)*i)+(0|(65535&i)*i)}e[0]=0|c[0]+(c[7]<<16|c[7]>>>16)+(c[6]<<16|c[6]>>>16),e[1]=0|c[1]+(c[0]<<8|c[0]>>>24)+c[7],e[2]=0|c[2]+(c[1]<<16|c[1]>>>16)+(c[0]<<16|c[0]>>>16),e[3]=0|c[3]+(c[2]<<8|c[2]>>>24)+c[1],e[4]=0|c[4]+(c[3]<<16|c[3]>>>16)+(c[2]<<16|c[2]>>>16),e[5]=0|c[5]+(c[4]<<8|c[4]>>>24)+c[3],e[6]=0|c[6]+(c[5]<<16|c[5]>>>16)+(c[4]<<16|c[4]>>>16),e[7]=0|c[7]+(c[6]<<8|c[6]>>>24)+c[5]}var i,r,a,c,o;e.exports=(e=n(12),n(16),n(17),n(27),n(28),i=(n=e).lib.StreamCipher,o=n.algo,r=[],a=[],c=[],o=o.RabbitLegacy=i.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]],r=this._b=0;r<4;r++)s.call(this);for(r=0;r<8;r++)i[r]^=n[7&r+4];if(t){var e=t.words,t=e[0],e=e[1],t=16711935&(t<<8|t>>>24)|4278255360&(t<<24|t>>>8),e=16711935&(e<<8|e>>>24)|4278255360&(e<<24|e>>>8),o=t>>>16|4294901760&e,a=e<<16|65535&t;i[0]^=t,i[1]^=o,i[2]^=e,i[3]^=a,i[4]^=t,i[5]^=o,i[6]^=e,i[7]^=a;for(r=0;r<4;r++)s.call(this)}},_doProcessBlock:function(e,t){var n=this._X;s.call(this),r[0]=n[0]^n[5]>>>16^n[3]<<16,r[1]=n[2]^n[7]>>>16^n[5]<<16,r[2]=n[4]^n[1]>>>16^n[7]<<16,r[3]=n[6]^n[3]>>>16^n[1]<<16;for(var i=0;i<4;i++)r[i]=16711935&(r[i]<<8|r[i]>>>24)|4278255360&(r[i]<<24|r[i]>>>8),e[t+i]^=r[i]},blockSize:4,ivSize:2}),n.RabbitLegacy=i._createHelper(o),e.RabbitLegacy)},function(e,l,u){!function(o,a){var c;!function(){"use strict";var e={"function":!0,"object":!0},k=e[typeof window]&&window||this,t=e[typeof o]&&o&&!o.nodeType&&o,e=e[typeof l]&&l&&t&&"object"==typeof a&&a,s=(!e||e.global!==e&&e.window!==e&&e.self!==e||(k=e),Math.pow(2,53)-1),M=/\bOpera/,t=Object.prototype,i=t.hasOwnProperty,P=t.toString;function n(e){return(e=String(e)).charAt(0).toUpperCase()+e.slice(1)}function F(e){return e=N(e),/^(?:webOS|i(?:OS|P))/.test(e)?e:n(e)}function R(e,t){for(var n in e)i.call(e,n)&&t(e[n],n,e)}function I(e){return null==e?n(e):P.call(e).slice(8,-1)}function L(e){return String(e).replace(/([ -])(?!$)/g,"$1?")}function D(n,i){var r=null,e=n,t=function(e,t){r=i(r,e,t,n)},o=-1,a=e?e.length:0;if("number"==typeof a&&-1<a&&a<=s)for(;++o<a;)t(e[o],o,e);else R(e,t);return r}function N(e){return String(e).replace(/^ +| +$/g,"")}function H(o){var t,n,e,i,r=k,a=o&&"object"==typeof o&&"String"!=I(o),s=(a&&(r=o,o=null),r.navigator||{}),c=s.userAgent||"",l=(o=o||c,a?!!s.likeChrome:/\bChrome\b/.test(o)&&!/internal|\n/i.test(P.toString())),u="Object",f=a?u:"ScriptBridgingProxyObject",d=a?u:"Environment",h=a&&r.java?"JavaPackage":I(r.java),u=a?u:"RuntimeObject",h=/\bJava/.test(h)&&r.java,d=h&&I(r.environment)==d,p=h?"a":"\u03b1",g=h?"b":"\u03b2",v=r.document||{},y=r.operamini||r.opera,a=M.test(a=a&&y?y["[[Class]]"]:I(y))?a:y=null,m=o,w=[],S=null,c=o==c,_=c&&y&&"function"==typeof y.version&&y.version(),C=D([{"label":"EdgeHTML","pattern":"Edge"},"Trident",{"label":"WebKit","pattern":"AppleWebKit"},"iCab","Presto","NetFront","Tasman","KHTML","Gecko"],function(e,t){return e||RegExp("\\b"+(t.pattern||L(t))+"\\b","i").exec(o)&&(t.label||t)}),b=D(["Adobe AIR","Arora","Avant Browser","Breach","Camino","Electron","Epiphany","Fennec","Flock","Galeon","GreenBrowser","iCab","Iceweasel","K-Meleon","Konqueror","Lunascape","Maxthon",{"label":"Microsoft Edge","pattern":"(?:Edge|Edg|EdgA|EdgiOS)"},"Midori","Nook Browser","PaleMoon","PhantomJS","Raven","Rekonq","RockMelt",{"label":"Samsung Internet","pattern":"SamsungBrowser"},"SeaMonkey",{"label":"Silk","pattern":"(?:Cloud9|Silk-Accelerated)"},"Sleipnir","SlimBrowser",{"label":"SRWare Iron","pattern":"Iron"},"Sunrise","Swiftfox","Vivaldi","Waterfox","WebPositive",{"label":"Yandex Browser","pattern":"YaBrowser"},{"label":"UC Browser","pattern":"UCBrowser"},"Opera Mini",{"label":"Opera Mini","pattern":"OPiOS"},"Opera",{"label":"Opera","pattern":"OPR"},"Chromium","Chrome",{"label":"Chrome","pattern":"(?:HeadlessChrome)"},{"label":"Chrome Mobile","pattern":"(?:CriOS|CrMo)"},{"label":"Firefox","pattern":"(?:Firefox|Minefield)"},{"label":"Firefox for iOS","pattern":"FxiOS"},{"label":"IE","pattern":"IEMobile"},{"label":"IE","pattern":"MSIE"},"Safari"],function(e,t){return e||RegExp("\\b"+(t.pattern||L(t))+"\\b","i").exec(o)&&(t.label||t)}),x=O([{"label":"BlackBerry","pattern":"BB10"},"BlackBerry",{"label":"Galaxy S","pattern":"GT-I9000"},{"label":"Galaxy S2","pattern":"GT-I9100"},{"label":"Galaxy S3","pattern":"GT-I9300"},{"label":"Galaxy S4","pattern":"GT-I9500"},{"label":"Galaxy S5","pattern":"SM-G900"},{"label":"Galaxy S6","pattern":"SM-G920"},{"label":"Galaxy S6 Edge","pattern":"SM-G925"},{"label":"Galaxy S7","pattern":"SM-G930"},{"label":"Galaxy S7 Edge","pattern":"SM-G935"},"Google TV","Lumia","iPad","iPod","iPhone","Kindle",{"label":"Kindle Fire","pattern":"(?:Cloud9|Silk-Accelerated)"},"Nexus","Nook","PlayBook","PlayStation Vita","PlayStation","TouchPad","Transformer",{"label":"Wii U","pattern":"WiiU"},"Wii","Xbox One",{"label":"Xbox 360","pattern":"Xbox"},"Xoom"]),B=D({"Apple":{"iPad":1,"iPhone":1,"iPod":1},"Alcatel":{},"Archos":{},"Amazon":{"Kindle":1,"Kindle Fire":1},"Asus":{"Transformer":1},"Barnes & Noble":{"Nook":1},"BlackBerry":{"PlayBook":1},"Google":{"Google TV":1,"Nexus":1},"HP":{"TouchPad":1},"HTC":{},"Huawei":{},"Lenovo":{},"LG":{},"Microsoft":{"Xbox":1,"Xbox One":1},"Motorola":{"Xoom":1},"Nintendo":{"Wii U":1,"Wii":1},"Nokia":{"Lumia":1},"Oppo":{},"Samsung":{"Galaxy S":1,"Galaxy S2":1,"Galaxy S3":1,"Galaxy S4":1},"Sony":{"PlayStation":1,"PlayStation Vita":1},"Xiaomi":{"Mi":1,"Redmi":1}},function(e,t,n){return e||(t[x]||t[/^[a-z]+(?: +[a-z]+\b)*/i.exec(x)]||RegExp("\\b"+L(n)+"(?:\\b|\\w*\\d)","i").exec(o))&&n}),T=D(["Windows Phone","KaiOS","Android","CentOS",{"label":"Chrome OS","pattern":"CrOS"},"Debian",{"label":"DragonFly BSD","pattern":"DragonFly"},"Fedora","FreeBSD","Gentoo","Haiku","Kubuntu","Linux Mint","OpenBSD","Red Hat","SuSE","Ubuntu","Xubuntu","Cygwin","Symbian OS","hpwOS","webOS ","webOS","Tablet OS","Tizen","Linux","Mac OS X","Macintosh","Mac","Windows 98;","Windows "],function(e,t){var n,i,r=t.pattern||L(t);return!e&&(e=RegExp("\\b"+r+"(?:/[\\d.]+|[ \\w.]*)","i").exec(o))&&(n=e,r=r,t=t.label||t,i={"10.0":"10","6.4":"10 Technical Preview","6.3":"8.1","6.2":"8","6.1":"Server 2008 R2 / 7","6.0":"Server 2008 / Vista","5.2":"Server 2003 / XP 64-bit","5.1":"XP","5.01":"2000 SP1","5.0":"2000","4.0":"NT","4.90":"ME"},r&&t&&/^Win/i.test(n)&&!/^Windows Phone /i.test(n)&&(i=i[/[\d.]+$/.exec(n)])&&(n="Windows "+i),n=String(n),e=n=F((n=r&&t?n.replace(RegExp(r,"i"),t):n).replace(/ ce$/i," CE").replace(/\bhpw/i,"web").replace(/\bMacintosh\b/,"Mac OS").replace(/_PowerPC\b/i," OS").replace(/\b(OS X) [^ \d]+/i,"$1").replace(/\bMac (OS X)\b/,"$1").replace(/\/(\d)/," $1").replace(/_/g,".").replace(/(?: BePC|[ .]*fc[ \d.]+)$/i,"").replace(/\bx86\.64\b/gi,"x86_64").replace(/\b(Windows Phone) OS\b/,"$1").replace(/\b(Chrome OS \w+) [\d.]+\b/,"$1").split(" on ")[0])),e});function O(e){return D(e,function(e,t){var n=t.pattern||L(t);return!e&&(e=RegExp("\\b"+n+" *\\d+[.\\w_]*","i").exec(o)||RegExp("\\b"+n+" *\\w+-[\\w]*","i").exec(o)||RegExp("\\b"+n+"(?:; *(?:[a-z]+[_-])?[a-z]+\\d+|[^ ();-]*)","i").exec(o))&&((e=String(t.label&&!RegExp(n,"i").test(t.label)?t.label:e).split("/"))[1]&&!/[\d.]+/.test(e[0])&&(e[0]+=" "+e[1]),t=t.label||t,e=F(e[0].replace(RegExp(n,"i"),t).replace(RegExp("; *(?:"+t+"[_-])?","i")," ").replace(RegExp("("+t+")[-_.]?(\\w)","i"),"$1 $2"))),e})}function A(e){return D(e,function(e,t){return e||(RegExp(t+"(?:-[\\d.]+/|(?: for [\\w-]+)?[ /-])([\\d.]+[^ ();/_-]*)","i").exec(o)||0)[1]||null})}if(C=C&&[C],/\bAndroid\b/.test(T)&&!x&&(t=/\bAndroid[^;]*;(.*?)(?:Build|\) AppleWebKit)\b/i.exec(o))&&(x=N(t[1]).replace(/^[a-z]{2}-[a-z]{2};\s*/i,"")||null),B&&!x?x=O([B]):B&&(x=x&&x.replace(RegExp("^("+L(B)+")[-_.\\s]","i"),B+" ").replace(RegExp("^("+L(B)+")[-_.]?(\\w)","i"),B+" $2")),(t=/\bGoogle TV\b/.exec(x))&&(x=t[0]),/\bSimulator\b/i.test(o)&&(x=(x?x+" ":"")+"Simulator"),"Opera Mini"==b&&/\bOPiOS\b/.test(o)&&w.push("running in Turbo/Uncompressed mode"),"IE"==b&&/\blike iPhone OS\b/.test(o)?(B=(t=H(o.replace(/like iPhone OS/,""))).manufacturer,x=t.product):/^iP/.test(x)?(b=b||"Safari",T="iOS"+((t=/ OS ([\d_]+)/i.exec(o))?" "+t[1].replace(/_/g,"."):"")):"Konqueror"==b&&/^Linux\b/i.test(T)?T="Kubuntu":B&&"Google"!=B&&(/Chrome/.test(b)&&!/\bMobile Safari\b/i.test(o)||/\bVita\b/.test(x))||/\bAndroid\b/.test(T)&&/^Chrome/.test(b)&&/\bVersion\//i.test(o)?(b="Android Browser",T=/\bAndroid\b/.test(T)?T:"Android"):"Silk"==b?(/\bMobi/i.test(o)||(T="Android",w.unshift("desktop mode")),/Accelerated *= *true/i.test(o)&&w.unshift("accelerated")):"UC Browser"==b&&/\bUCWEB\b/.test(o)?w.push("speed mode"):"PaleMoon"==b&&(t=/\bFirefox\/([\d.]+)\b/.exec(o))?w.push("identifying as Firefox "+t[1]):"Firefox"==b&&(t=/\b(Mobile|Tablet|TV)\b/i.exec(o))?(T=T||"Firefox OS",x=x||t[1]):!b||(t=!/\bMinefield\b/i.test(o)&&/\b(?:Firefox|Safari)\b/.exec(b))?(b&&!x&&/[\/,]|^[^(]+?\)/.test(o.slice(o.indexOf(t+"/")+8))&&(b=null),(t=x||B||T)&&(x||B||/\b(?:Android|Symbian OS|Tablet OS|webOS)\b/.test(T))&&(b=/[a-z]+(?: Hat)?/i.exec(/\bAndroid\b/.test(T)?T:t)+" Browser")):"Electron"==b&&(t=(/\bChrome\/([\d.]+)\b/.exec(o)||0)[1])&&w.push("Chromium "+t),_=_||A(["(?:Cloud9|CriOS|CrMo|Edge|Edg|EdgA|EdgiOS|FxiOS|HeadlessChrome|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\d.]+$)|UCBrowser|YaBrowser)","Version",L(b),"(?:Firefox|Minefield|NetFront)"]),(t=("iCab"==C&&3<parseFloat(_)?"WebKit":/\bOpera\b/.test(b)&&(/\bOPR\b/.test(o)?"Blink":"Presto"))||(/\b(?:Midori|Nook|Safari)\b/i.test(o)&&!/^(?:Trident|EdgeHTML)$/.test(C)?"WebKit":!C&&/\bMSIE\b/i.test(o)&&("Mac OS"==T?"Tasman":"Trident"))||"WebKit"==C&&/\bPlayStation\b(?! Vita\b)/i.test(b)&&"NetFront")&&(C=[t]),"IE"==b&&(t=(/; *(?:XBLWP|ZuneWP)(\d+)/i.exec(o)||0)[1])?(b+=" Mobile",T="Windows Phone "+(/\+$/.test(t)?t:t+".x"),w.unshift("desktop mode")):/\bWPDesktop\b/i.test(o)?(b="IE Mobile",T="Windows Phone 8.x",w.unshift("desktop mode"),_=_||(/\brv:([\d.]+)/.exec(o)||0)[1]):"IE"!=b&&"Trident"==C&&(t=/\brv:([\d.]+)/.exec(o))&&(b&&w.push("identifying as "+b+(_?" "+_:"")),b="IE",_=t[1]),c){if(i="global",E=null!=(e=r)?typeof e[i]:"number",/^(?:boolean|number|string|undefined)$/.test(E)||"object"==E&&!e[i])I(t=r.runtime)==f?(b="Adobe AIR",T=t.flash.system.Capabilities.os):I(t=r.phantom)==u?(b="PhantomJS",_=(t=t.version||null)&&t.major+"."+t.minor+"."+t.patch):"number"==typeof v.documentMode&&(t=/\bTrident\/(\d+)/i.exec(o))?(_=[_,v.documentMode],(t=+t[1]+4)!=_[1]&&(w.push("IE "+_[1]+" mode"),C&&(C[1]=""),_[1]=t),_="IE"==b?String(_[1].toFixed(1)):_[0]):"number"==typeof v.documentMode&&/^(?:Chrome|Firefox)\b/.test(b)&&(w.push("masking as "+b+" "+_),b="IE",_="11.0",C=["Trident"],T="Windows");else if(h&&(m=(t=h.lang.System).getProperty("os.arch"),T=T||t.getProperty("os.name")+" "+t.getProperty("os.version")),d){try{_=r.require("ringo/engine").version.join("."),b="RingoJS"}catch(e){(t=r.system)&&t.global.system==r.system&&(b="Narwhal",T=T||t[0].os||null)}b=b||"Rhino"}else"object"==typeof r.process&&!r.process.browser&&(t=r.process)&&("object"==typeof t.versions&&("string"==typeof t.versions.electron?(w.push("Node "+t.versions.node),b="Electron",_=t.versions.electron):"string"==typeof t.versions.nw&&(w.push("Chromium "+_,"Node "+t.versions.node),b="NW.js",_=t.versions.nw)),b||(b="Node.js",m=t.arch,T=t.platform,_=(_=/[\d.]+/.exec(t.version))?_[0]:null));T=T&&F(T)}if(_&&(t=/(?:[ab]|dp|pre|[ab]\d+pre)(?:\d+\+?)?$/i.exec(_)||/(?:alpha|beta)(?: ?\d)?/i.exec(o+";"+(c&&s.appMinorVersion))||/\bMinefield\b/i.test(o)&&"a")&&(S=/b/i.test(t)?"beta":"alpha",_=_.replace(RegExp(t+"\\+?$"),"")+("beta"==S?g:p)+(/\d+\+?/.exec(t)||"")),"Fennec"==b||"Firefox"==b&&/\b(?:Android|Firefox OS|KaiOS)\b/.test(T))b="Firefox Mobile";else if("Maxthon"==b&&_)_=_.replace(/\.[\d.]+/,".x");else if(/\bXbox\b/i.test(x))"Xbox 360"==x&&(T=null),"Xbox 360"==x&&/\bIEMobile\b/.test(o)&&w.unshift("mobile mode");else if(!/^(?:Chrome|IE|Opera)$/.test(b)&&(!b||x||/Browser|Mobi/.test(b))||"Windows CE"!=T&&!/Mobi/i.test(o))if("IE"==b&&c)try{null===r.external&&w.unshift("platform preview")}catch(e){w.unshift("embedded")}else(/\bBlackBerry\b/.test(x)||/\bBB10\b/.test(o))&&(t=(RegExp(x.replace(/ +/g," *")+"/([.\\d]+)","i").exec(o)||0)[1]||_)?(T=((t=[t,/BB10/.test(o)])[1]?(x=null,B="BlackBerry"):"Device Software")+" "+t[0],_=null):this!=R&&"Wii"!=x&&(c&&y||/Opera/.test(b)&&/\b(?:MSIE|Firefox)\b/i.test(o)||"Firefox"==b&&/\bOS X (?:\d+\.){2,}/.test(T)||"IE"==b&&(T&&!/^Win/.test(T)&&5.5<_||/\bWindows XP\b/.test(T)&&8<_||8==_&&!/\bTrident\b/.test(o)))&&!M.test(t=H.call(R,o.replace(M,"")+";"))&&t.name&&(t="ing as "+t.name+((t=t.version)?" "+t:""),M.test(b)?(/\bIE\b/.test(t)&&"Mac OS"==T&&(T=null),t="identify"+t):(t="mask"+t,b=a?F(a.replace(/([a-z])([A-Z])/g,"$1 $2")):"Opera",/\bIE\b/.test(t)&&(T=null),c||(_=null)),C=["Presto"],w.push(t));else b+=" Mobile";(t=(/\bAppleWebKit\/([\d.]+\+?)/i.exec(o)||0)[1])&&(t=[parseFloat(t.replace(/\.(\d)$/,".0$1")),t],"Safari"==b&&"+"==t[1].slice(-1)?(b="WebKit Nightly",S="alpha",_=t[1].slice(0,-1)):_!=t[1]&&_!=(t[2]=(/\bSafari\/([\d.]+\+?)/i.exec(o)||0)[1])||(_=null),t[1]=(/\b(?:Headless)?Chrome\/([\d.]+)/i.exec(o)||0)[1],537.36==t[0]&&537.36==t[2]&&28<=parseFloat(t[1])&&"WebKit"==C&&(C=["Blink"]),t=c&&(l||t[1])?(C&&(C[1]="like Chrome"),t[1]||((t=t[0])<530?1:t<532?2:t<532.05?3:t<533?4:t<534.03?5:t<534.07?6:t<534.1?7:t<534.13?8:t<534.16?9:t<534.24?10:t<534.3?11:t<535.01?12:t<535.02?"13+":t<535.07?15:t<535.11?16:t<535.19?17:t<536.05?18:t<536.1?19:t<537.01?20:t<537.11?"21+":t<537.13?23:t<537.18?24:t<537.24?25:t<537.36?26:"Blink"!=C?"27":"28")):(C&&(C[1]="like Safari"),(t=t[0])<400?1:t<500?2:t<526?3:t<533?4:t<534?"4+":t<535?5:t<537?6:t<538?7:t<601?8:t<602?9:t<604?10:t<606?11:t<608?12:"12"),C&&(C[1]+=" "+(t+="number"==typeof t?".x":/[.+]/.test(t)?"":"+")),"Safari"==b&&(!_||45<parseInt(_))?_=t:"Chrome"==b&&/\bHeadlessChrome/i.test(o)&&w.unshift("headless")),"Opera"==b&&(t=/\bzbov|zvav$/.exec(T))?(b+=" ",w.unshift("desktop mode"),"zvav"==t?(b+="Mini",_=null):b+="Mobile",T=T.replace(RegExp(" *"+t+"$"),"")):"Safari"==b&&/\bChrome\b/.exec(C&&C[1])?(w.unshift("desktop mode"),b="Chrome Mobile",_=null,T=/\bOS X\b/.test(T)?(B="Apple","iOS 4.3+"):null):/\bSRWare Iron\b/.test(b)&&!_&&(_=A("Chrome")),(T=_&&0==_.indexOf(t=/[\d.]+$/.exec(T))&&-1<o.indexOf("/"+t+"-")?N(T.replace(t,"")):T)&&-1!=T.indexOf(b)&&!RegExp(b+" OS").test(T)&&(T=T.replace(RegExp(" *"+L(b)+" *"),"")),C&&!/\b(?:Avant|Nook)\b/.test(b)&&(/Browser|Lunascape|Maxthon/.test(b)||"Safari"!=b&&/^iOS/.test(T)&&/\bSafari\b/.test(C[1])||/^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|SRWare Iron|Vivaldi|Web)/.test(b)&&C[1])&&(t=C[C.length-1])&&w.push(t),w.length&&(w=["("+w.join("; ")+")"]),B&&x&&x.indexOf(B)<0&&w.push("on "+B),x&&w.push((/^on /.test(w[w.length-1])?"":"on ")+x),T&&(t=/ ([\d.+]+)$/.exec(T),n=t&&"/"==T.charAt(T.length-t[0].length-1),T={"architecture":32,"family":t&&!n?T.replace(t[0],""):T,"version":t?t[1]:null,"toString":function(){var e=this.version;return this.family+(e&&!n?" "+e:"")+(64==this.architecture?" 64-bit":"")}}),(t=/\b(?:AMD|IA|Win|WOW|x86_|x)64\b/i.exec(m))&&!/\bi686\b/i.test(m)?(T&&(T.architecture=64,T.family=T.family.replace(RegExp(" *"+t),"")),b&&(/\bWOW64\b/i.test(o)||c&&/\w(?:86|32)$/.test(s.cpuClass||s.platform)&&!/\bWin64; x64\b/i.test(o))&&w.unshift("32-bit")):T&&/^OS X/.test(T.family)&&"Chrome"==b&&39<=parseFloat(_)&&(T.architecture=64),o=o||null;var E={};return E.description=o,E.layout=C&&C[0],E.manufacturer=B,E.name=b,E.prerelease=S,E.product=x,E.ua=o,E.version=b&&_,E.os=T||{"architecture":null,"family":null,"version":null,"toString":function(){return"null"}},E.parse=H,E.toString=function(){return this.description||""},E.version&&w.unshift(_),E.name&&w.unshift(b),T&&b&&(T!=String(T).split(" ")[0]||T!=b.split(" ")[0]&&!x)&&w.push(x?"("+T+")":"on "+T),w.length&&(E.description=w.join(" ")),E}var r=H();k.platform=r,void 0!==(c=function(){return r}.call(l,u,l,o))&&(o.exports=c)}.call(this)}.call(this,u(46)(e),u(3))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";t.__esModule=!0;t=n(132),n=document.querySelector(".footprintGift");new t["default"](n)},function(e,t,n){"use strict";var i,o,a=this&&this.__assign||function(){return(a=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},s=this&&this.__awaiter||function(e,a,s,c){return new(s=s||Promise)(function(n,t){function i(e){try{o(c.next(e))}catch(e){t(e)}}function r(e){try{o(c["throw"](e))}catch(e){t(e)}}function o(e){var t;e.done?n(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(i,r)}o((c=c.apply(e,a||[])).next())})},c=this&&this.__generator||function(i,r){var o,a,s,c={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},e={next:t(0),"throw":t(1),"return":t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(n){return function(e){var t=[n,e];if(o)throw new TypeError("Generator is already executing.");for(;c;)try{if(o=1,a&&(s=2&t[0]?a["return"]:t[0]?a["throw"]||((s=a["return"])&&s.call(a),0):a.next)&&!(s=s.call(a,t[1])).done)return s;switch(a=0,(t=s?[2&t[0],s.value]:t)[0]){case 0:case 1:s=t;break;case 4:return c.label++,{value:t[1],done:!1};case 5:c.label++,a=t[1],t=[0];continue;case 7:t=c.ops.pop(),c.trys.pop();continue;default:if(!(s=0<(s=c.trys).length&&s[s.length-1])&&(6===t[0]||2===t[0])){c=0;continue}if(3===t[0]&&(!s||t[1]>s[0]&&t[1]<s[3]))c.label=t[1];else if(6===t[0]&&c.label<s[1])c.label=s[1],s=t;else{if(!(s&&c.label<s[2])){s[2]&&c.ops.pop(),c.trys.pop();continue}c.label=s[2],c.ops.push(t)}}t=r.call(i,c)}catch(e){t=[6,e],a=0}finally{o=s=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}}},r=(t.__esModule=!0,n(133)),l=n(5),u=n(136),n=((n=i=i||{})[n["CefShowOnlinePage"]=1]="CefShowOnlinePage",n[n["CefOpenHtml"]=2]="CefOpenHtml",n[n["CefStartFlashToolBox"]=3]="CefStartFlashToolBox",n[n["CefOpenFCPage"]=4]="CefOpenFCPage",(n=o=o||{})["CARD"]="card",n["CLOUD_COUPON"]="cloud_coupon",n["GAME_COUPON"]="game_coupon",f.prototype.init=function(){return s(this,void 0,void 0,function(){var t=this;return c(this,function(e){try{top["onShowGameWelfare"]=function(i){return s(t,void 0,void 0,function(){var t,n;return c(this,function(e){switch(e.label){case 0:return console.log("\u6211\u4eac\u54e5\u8c03\u7528onShowGameWelfare\u65b9\u6cd5\u4e86",i,typeof i),this.isShowDown?[3,3]:[4,(0,l.jsonp)("//game.flash.cn/gameact/foot-print-act/info?game_id="+(null!==game_id&&void 0!==game_id?game_id:1106))];case 1:return(t=e.sent(),console.log("info",t),200!==t.code)?[2]:("a"===t.data.side&&(this.giftList=t.data,this.showAPage()),"b"!==t.data.side?[3,3]:[4,(0,l.jsonp)("//game.flash.cn/v1/card/game-card-list?game_id="+(null!==game_id&&void 0!==game_id?game_id:1106))]);case 2:t=e.sent(),this.showBPage(t),e.label=3;case 3:return i&&i.isCountdown?this.startCountdown():(console.log("\u770b\u4e00\u4e0b\u6536\u8d77\u6309\u94ae\u662f\u4e0d\u662f\u8fd8\u662f\u4e0d\u5b58\u5728",this.closeEl),this.wrap.querySelector(".close")?this.wrap.querySelector(".close").innerHTML="\u6536\u8d77":((n=document.createElement("span")).className="close",n.innerHTML="\u6536\u8d77",this.wrap.appendChild(n),this.closeEl=n)),this.toggleDown(),[2]}})})}}catch(e){}return this.wrap.addEventListener("click",function(e){e=e.target;"li"===e.tagName.toLowerCase()||"gift-link"===e.className?t.handleClick(e):"h3"===e.tagName.toLowerCase()?t.handleClick(e.parentNode):e.classList.contains("close")||e.classList.contains("countdownTime")?(t.countdown=0,clearInterval(t.clearIntervalId),t.closeEl&&(t.closeEl.innerHTML="\u6536\u8d77"),t.toggleDown()):"gift-item-button"===e.className?t.getGiftData(e):"gift-card-copy"===e.className?t.CopyToClipboard(e.parentNode.querySelector("input")):"gift-card-method"===e.className&&t.showGiftPop(e)}),this.wrap.addEventListener("mouseenter",function(){console.log("mouseenter"),t.pauseCountdown()}),this.wrap.addEventListener("mouseleave",function(){console.log("mouseleave"),t.resumeCountdown()}),(0,r.notifyClientPageLoaded)(),[2]})})},f.prototype.showGiftPop=function(i){return s(this,void 0,void 0,function(){var t,n=this;return c(this,function(e){switch(e.label){case 0:return(t=i.dataset.cardId,this.clickState)?[2]:(this.clickState=!0,[4,(0,l.jsonp)("//game.flash.cn/vip/api/card-exchange-method?type=1&card_id="+t)]);case 1:return t=e.sent(),this.clickState=!1,200!==t.code?this.message(t.message):(this.giftPop||(this.giftPop=document.createElement("div"),this.giftPop.className="gift-pop",document.body.appendChild(this.giftPop),this.giftPop.addEventListener("click",function(e){e=e.target;console.log("target",e),"gift-pop-close"!==e.className&&"svg"!==e.tagName.toLowerCase()&&"path"!==e.tagName.toLowerCase()||(n.giftPop.classList.add("hidden"),n.giftPop.innerHTML="")})),this.giftPop.innerHTML='<div class="gift-pop-content">\n        <div class="gift-pop-header">\n          <h2>\u9886\u53d6\u793c\u5305\u6b65\u9aa4</h2>\n          <span class="gift-pop-close"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">\n              <path\n                d="M12.71,12l5.14-5.15a.49.49,0,1,0-.7-.7L12,11.29,6.85,6.15a.49.49,0,0,0-.7.7L11.29,12,6.15,17.15a.48.48,0,0,0,0,.7.48.48,0,0,0,.7,0L12,12.71l5.15,5.14a.48.48,0,0,0,.7,0,.48.48,0,0,0,0-.7Z"></path></svg></span>\n        </div>\n        <div class="gift-pop-body">\n          '+t.text+"\n        </div>\n      </div>",this.giftPop.classList.remove("hidden")),[2]}})})},f.prototype.getGiftData=function(i){return s(this,void 0,void 0,function(){var t,n;return c(this,function(e){switch(e.label){case 0:return(t=i.dataset.id,i.dataset.type===o.CLOUD_COUPON&&"\u7acb\u5373\u4f7f\u7528"===i.innerHTML)?(this.getToolInfoOpen(7),[2]):this.clickState?[2]:(this.clickState=!0,[4,(0,l.jsonp)("//game.flash.cn/gameact/foot-print-act/get-gift?game_id="+(null!==game_id&&void 0!==game_id?game_id:1106)+"&id="+t)]);case 1:return 200!==(t=e.sent()).code?(this.message(t.message),this.clickState=!1,[2]):[4,(0,l.jsonp)("//game.flash.cn/gameact/foot-print-act/info?game_id="+(null!==game_id&&void 0!==game_id?game_id:1106))];case 2:return n=e.sent(),this.clickState=!1,"a"===n.data.side&&(this.giftList=n.data,this.showAPage()),"b"!==n.data.side?[3,4]:[4,(0,l.jsonp)("//game.flash.cn/v1/card/game-card-list?game_id="+(null!==game_id&&void 0!==game_id?game_id:1106))];case 3:n=e.sent(),this.showBPage(n),e.label=4;case 4:return[2]}})})},f.prototype.handleClick=function(e){e=e.dataset.data,e=JSON.parse(e);e.type===i.CefShowOnlinePage?(0,u.cefClientFn)("CefShowOnlinePage",{url:""+u.onnlinePageUrl+e.url}):e.type===i.CefOpenHtml?(0,u.cefClientFn)("CefOpenHtml",{url:e.url}):e.type===i.CefStartFlashToolBox?this.getToolInfoOpen(e.appid,e.url):e.type===i.CefOpenFCPage&&(0,u.cefClientFn)("CefOpenFCPage",{path:e.path})},f.prototype.getToolInfoOpen=function(n,i){var r;return void 0===i&&(i=""),s(this,void 0,void 0,function(){var t;return c(this,function(e){switch(e.label){case 0:return this.clickState?[2]:(this.clickState=!0,[4,(0,l.jsonp)("https://apifcv2.flash.cn/container/list")]);case 1:return t=e.sent(),console.log("tools",t),t=null==(r=null==t?void 0:t.data)?void 0:r.find(function(e){return e.appid===n}),this.clickState=!1,t&&(0,u.openFlashToolBox)(a(a({},t),{url:i}),29),[2]}})})},f.prototype.showAPage=function(){return s(this,void 0,void 0,function(){var i,r=this;return c(this,function(e){return i="",this.giftList.list.forEach(function(e,t){var n;e.type===o.CARD&&(n='<div class="gift-desc">\n            '+e.content+"\n            </div>",e.card_config&&(n='<div class="gift-card">\n            \u5151\u6362\u7801\uff1a<input type="text" class="gift-card-input" value="'+e.card_config.card+'" readonly>\n            <button class="gift-card-copy">\u590d\u5236</button>\n            </div><div class="gift-card">\u5151\u6362\u65b9\u5f0f\uff1a<span class="gift-card-method" data-card-id="'+e.card_config.card_id+'">\u70b9\u51fb\u67e5\u770b</span></div>'),i+='<div class="gift-item">\n          <div class="gift-item-icon gift">\n            <button class="gift-item-button'+(t+1>r.giftList.day||0!==e.status?" disabled":"")+'" data-id="'+e.id+'">'+(0===e.status?"\u9886\u53d6":"\u5df2\u9886\u53d6")+'</button>\n          </div>\n          <div class="gift-item-content">\n            <h2 class="gift-item-header">\n              <em>\u7b2c<span>'+(t+1)+"</span>\u65e5</em>"+e.name+"\n            </h2>\n            "+n+"\n          </div>\n        </div>"),e.type===o.GAME_COUPON&&(i+='<div class="gift-item">\n          <div class="gift-item-icon coupon">\n            <button class="gift-item-button'+(t+1>r.giftList.day||0!==e.status?" disabled":"")+'" data-id="'+e.id+'">'+(0===e.status?"\u9886\u53d6":"\u5df2\u9886\u53d6")+'</button>\n          </div>\n          <div class="gift-item-content">\n            <h2 class="gift-item-header">\n              <em>\u7b2c<span>'+(t+1)+"</span>\u65e5</em>"+e.name+'\n            </h2>\n            <div class="gift-coupon-desc">\n              <b>\xa5'+e.coupon_config.money+"</b>\u6ee1"+e.coupon_config.threshold_money+"\u5143\u53ef\u7528<span>"+(1===e.status?"\u672a\u4f7f\u7528":2===e.status?"\u5df2\u4f7f\u7528":"")+'</span>\n            </div>\n            <div class="gift-coupon-tip">\u6e38\u620f\u5185\u626b\u7801\u5145\u503c\u4f7f\u7528</div>\n          </div>\n        </div>'),e.type===o.CLOUD_COUPON&&(i+='<div class="gift-item">\n          <div class="gift-item-icon cloud">\n            <button class="gift-item-button'+(t+1>r.giftList.day?" disabled":"")+'" data-id="'+e.id+'" data-type="'+e.type+'">'+(0===e.status?"\u9886\u53d6":"\u7acb\u5373\u4f7f\u7528")+'</button>\n          </div>\n          <div class="gift-item-content">\n            <h2 class="gift-item-header">\n              <em>\u7b2c<span>'+(t+1)+"</span>\u65e5</em>"+e.name+'\n            </h2>\n            <div class="gift-cloud-desc">\n              \u652f\u63017x24\u5c0f\u65f6\u6302\u673a<span class="gift-link" data-data=\'{"type":2,"url":"https://game.flash.cn/platform-notice-detail/755433"}\'>\u4e86\u89e3\u4e91\u6302\u673a</span>\n            </div>\n          </div>\n        </div>'),t<r.giftList.list.length-1&&(i+="<span class='gift-item-line"+(t+1>=r.giftList.day?" disabled":"")+"'></span>")}),this.wrap.innerHTML='<div class="gift-list">'+i+"</div>",console.log("a\u9875\u9762\u663e\u793a"),[2]})})},f.prototype.showBPage=function(t){var n;return s(this,void 0,void 0,function(){return c(this,function(e){return console.log("b\u9875\u9762\u663e\u793a",t),this.wrap.innerHTML='<ul class="a-page">\n        <li data-data=\'{"type":1,"url":"/welfare?title=\u6bcf\u65e5\u7b7e\u5230"}\'>\n          <h3>\u6bcf\u65e5\u7b7e\u5230</h3>\n          \u79ef\u5206\u5151\u6362\u5956\u54c1\n        </li>\n        <li data-data=\'{"type":1,"url":"/welfare/firstCharge?title=\u9996\u5145\u8d5e\u52a9\u5361"}\'>\n          <h3>\u9996\u5145\u8d5e\u52a9\u5361</h3>\n          \u6700\u9ad8\u53ef\u7701235\u5143\n        </li>\n        <li data-data=\'{"type":1,"url":"/welfare/gift?gid='+game_id+"&title=\u6e38\u620f\u793c\u5305\u5408\u96c6\"}'>\n          <h3>"+game_name+"\u6e38\u620f\u793c\u5305</h3>\n          \u5171"+(null==(n=null==t?void 0:t.data)?void 0:n.length)+'\u4e2a\u793c\u5305\u53ef\u9886\u53d6\n        </li>\n        <li data-data=\'{"type":1,"url":"/vip/welfare?title=\u7279\u6743\u4ecb\u7ecd"}\'>\n          <h3>VIP\u7279\u6743</h3>\n          \u6bcf\u59294\u5c0f\u65f6\u514d\u8d39\u4f7f\u7528\n        </li>\n        <li data-data=\'{"type":3,"appid":7,"url":""}\'>\n          <h3>\u4e91\u6302\u673a</h3>\n          \u6bcf\u59294\u5c0f\u65f6\u514d\u8d39\u4f7f\u7528\n        </li>\n        <li data-data=\'{"type":3,"appid":6,"url":""}\'>\n          <h3>\u8fdc\u7a0b\u5de5\u5177</h3>\n          \u6bcf\u59294\u5c0f\u65f6\u514d\u8d39\u4f7f\u7528\n        </li>\n        <li  data-data=\'{"type":2,"url":"https://kf.flash.cn/"}\'>\n          <h3>\u5728\u7ebf\u5ba2\u670d</h3>\n          7x12\u5c0f\u65f6\u670d\u52a1\n        </li>\n      </ul>',[2]})})},f.prototype.toggleDown=function(){var e=document.body;e.classList.contains("gift")||this.isShowDown?(e.classList.remove("gift"),this.isShowDown=!1):(e.classList.add("gift"),this.isShowDown=!0)},f.prototype.startCountdown=function(){var e;console.log("\u8fd9\u4e2a\u662f\u5012\u8ba1\u65f6\u6536\u8d77\u6309\u94ae",this.closeEl),this.countdown=15,this.wrap.querySelector(".close")?this.wrap.querySelector(".close").innerHTML='<span class="countdownTime">'+this.countdown+"</span>s\u540e\u6536\u8d77":((e=document.createElement("span")).className="close",e.innerHTML='<span class="countdownTime">'+this.countdown+"</span>s\u540e\u6536\u8d77",this.wrap.appendChild(e),this.closeEl=e),this.resumeCountdown()},f.prototype.pauseCountdown=function(){this.countdown<=0||this.clearIntervalId&&(clearInterval(this.clearIntervalId),this.clearIntervalId=null)},f.prototype.resumeCountdown=function(){var e=this;this.countdown<=0||this.clearIntervalId||(this.clearIntervalId=setInterval(function(){e.countdown--,e.closeEl&&(e.closeEl.querySelector(".countdownTime").innerHTML=e.countdown+""),e.countdown<=0&&(clearInterval(e.clearIntervalId),e.clearIntervalId=null,e.toggleDown())},1e3))},f.prototype.message=function(e){var t,n,i=this;this.status||(this.status=!0,t=document,(n=t.createElement("div")).className="tipsBox _fadeIn",n.innerHTML=e,t.body.appendChild(n),setTimeout(function(){n.classList.remove("_fadeIn"),n.classList.add("_fadeOut"),setTimeout(function(){i.status=!1,n.parentNode.removeChild(n)},1e3)},2e3))},f.prototype.CopyToClipboard=function(e,t){var n=window,i=document,r=e.value;n["clipboardData"]?(n["clipboardData"].clearData(),n["clipboardData"].setData("Text",r),this.message("\u590d\u5236\u6210\u529f\uff01")):e.select&&i.execCommand&&(e.select(),e.setSelectionRange(0,e.value.length),i.execCommand("Copy")?t?t():this.message("\u590d\u5236\u6210\u529f\uff01"):this.message("\u590d\u5236\u64cd\u4f5c\u4e0d\u88ab\u652f\u6301\uff0c\u8bf7\u53cc\u51fb\u5185\u5bb9\u590d\u5236\uff01"))},f);function f(e){this.isShowDown=!1,this.countdown=0,this.clearIntervalId=null,this.closeEl=null,this.giftPop=null,e&&(this.wrap=e,this.clickState=!1,this.init())}t["default"]=n},function(e,i,t){"use strict";var n=this&&this.__assign||function(){return(n=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},r=this&&this.__awaiter||function(e,a,s,c){return new(s=s||Promise)(function(n,t){function i(e){try{o(c.next(e))}catch(e){t(e)}}function r(e){try{o(c["throw"](e))}catch(e){t(e)}}function o(e){var t;e.done?n(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(i,r)}o((c=c.apply(e,a||[])).next())})},o=this&&this.__generator||function(i,r){var o,a,s,c={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},e={next:t(0),"throw":t(1),"return":t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(n){return function(e){var t=[n,e];if(o)throw new TypeError("Generator is already executing.");for(;c;)try{if(o=1,a&&(s=2&t[0]?a["return"]:t[0]?a["throw"]||((s=a["return"])&&s.call(a),0):a.next)&&!(s=s.call(a,t[1])).done)return s;switch(a=0,(t=s?[2&t[0],s.value]:t)[0]){case 0:case 1:s=t;break;case 4:return c.label++,{value:t[1],done:!1};case 5:c.label++,a=t[1],t=[0];continue;case 7:t=c.ops.pop(),c.trys.pop();continue;default:if(!(s=0<(s=c.trys).length&&s[s.length-1])&&(6===t[0]||2===t[0])){c=0;continue}if(3===t[0]&&(!s||t[1]>s[0]&&t[1]<s[3]))c.label=t[1];else if(6===t[0]&&c.label<s[1])c.label=s[1],s=t;else{if(!(s&&c.label<s[2])){s[2]&&c.ops.pop(),c.trys.pop();continue}c.label=s[2],c.ops.push(t)}}t=r.call(i,c)}catch(e){t=[6,e],a=0}finally{o=s=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}}},a=(i.__esModule=!0,i.getCDMStatus=i.installCDM=i.setBootupRepair=i.needShowBootupRepairChk=i.addHSToFirewall=i.isHSInFirewall=i.getSearchRecord=i.addSearchRecord=i.clearSearchRecord=i.notifyClientPageLoaded=i.stopRepair=i.startRepair=i.startCheck=i.onFlashOpeResult=i.tracker=i.Tracker=i.encrypt=i.openUrlByMiniBrowser=i.notifyClientChangePasswordSuccess=i.showChangePasswordWindow=i.getHistorySwf=i.openSwfSuccess=i.deleSwfFile=i.runSwfFile=i.openFlashSettingsWindow=i.openInstallFlashWindow=i.removeClientGameFile=i.openDownLoadManager=i.openSettingsWindow=i.openUpdateWindow=i.onGlobalConfigChangeAsync=i.getGlobalConfigAsync=i.getGlobalConfig=i.onOpenGame=i.openGame=i.openFindGame=i.isFC_CloseLoadingPage=i.directlyOpenGame=i.minimizeWindow=i.restoreWindow=i.maximizeWindow=i.screenCapture=i.closeWindow=i.toggleMaximizeAndNormal=i.startDragWindow=i.getScaleFactor=i.evaluateInMainWindow=i.getUserVerify=i.userVerifySuccess=i.toggleUserVerify=void 0,i.onOderResponse=i.cefOderRequest=i.openIe=i.openIEError=i.onInstallCDMProcess=void 0,t(134)),s=t(135),c=(l.prototype.on=function(e){return this.listeners.push(e),this},l.prototype.off=function(t){return this.listeners=void 0===t?[]:this.listeners.filter(function(e){return e!==t}),this},l.prototype.once=function(t){function n(e){i.off(n),t(e)}var i=this;return this.listeners.push(n),this},l.prototype.emit=function(t){return this.listeners.forEach(function(e){e(t)}),this},l);function l(){this.listeners=[]}var u=top.external;function f(e){var t,n={};for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&null!=e[t]&&(n[t]=e[t]);return n}function d(e,t){void 0===t&&(t=0);var n=new c;return top[e]=function(e){setTimeout(function(){n.emit(e)},t)},n}i.toggleUserVerify=function(){if("function"==typeof u.FC_UserVerify_Center)return u.FC_UserVerify_Center();console.log("\u4e2a\u4eba\u4e2d\u5fc3\u8c03\u8d77\u5b9e\u540d\u8ba4\u8bc1\u5931\u8d25")},i.userVerifySuccess=d("onFC_UserVerify_Center"),i.getUserVerify=function(){if("function"==typeof u.FC_UserVerify_CenterShow)return u.FC_UserVerify_CenterShow();console.log("\u4e2a\u4eba\u4e2d\u5fc3\u8c03\u8d77\u67e5\u770b\u5b9e\u540d\u8ba4\u8bc1\u5931\u8d25")},i.evaluateInMainWindow=function(e){if("function"==typeof u.FC_OpenGameOfficialWebsite)return u.FC_OpenGameOfficialWebsite("(function(){"+e+"})");console.log("\u4e3b\u7a97\u53e3\u6267\u884cjs\u4ee3\u7801\u5931\u8d25",e)},i.getScaleFactor=function(){return"function"==typeof u.FC_WindowScaleFactor?u.FC_WindowScaleFactor():1},i.startDragWindow=function(){if("function"==typeof u.FC_MouseMove)return u.FC_MouseMove();console.log("\u5f00\u59cb\u62d6\u62fd\u7a97\u53e3")},i.toggleMaximizeAndNormal=function(){if("function"==typeof u.FC_MouseDoubleClick)return u.FC_MouseDoubleClick();console.log("\u7a97\u53e3\u5728\u666e\u901a\u72b6\u6001\u548c\u6700\u5927\u5316\u5207\u6362")},i.closeWindow=function(){if("function"==typeof u.FC_ClickCloseBtn)return u.FC_ClickCloseBtn();console.log("\u5173\u95ed\u7a97\u53e3")};var h=d("onGameShot"),p=0;function g(e){if("function"==typeof u.FC_OpenGame)return u.FC_OpenGame(JSON.stringify(e));console.log("\u6253\u5f00\u6e38\u620f",e)}function v(){if("function"==typeof u.FC_GetGlobalConfig)try{return JSON.parse(u.FC_GetGlobalConfig())}catch(e){}return console.log("\u83b7\u53d6\u540c\u6b65\u914d\u7f6e\u4fe1\u606f"),{}}i.screenCapture=function(){return"function"==typeof u.FC_GameShot?new Promise(function(r){var o;h.on(function(e){var t,n,i;!e||Object.keys(e).length<=0||(t=e.data,n=e.pieceCount,i=e.pieceIndex,e.sourceID===p&&(null==o&&(o=new Array(n)),i<n&&(o[i]=t),n-1<=i)&&(r(o.join("")),h.off()))}),u.FC_GameShot(++p+"")}):(console.log("\u622a\u5c4f\u5931\u8d25"),Promise.resolve(""))},i.maximizeWindow=function(){if("function"==typeof u.FC_ClickMaxBtn)return u.FC_ClickMaxBtn();console.log("\u6700\u5927\u5316\u7a97\u53e3")},i.restoreWindow=function(){if("function"==typeof u.FC_ClickRestoreBtn)return u.FC_ClickRestoreBtn();console.log("\u8fd8\u539f\u7a97\u53e3")},i.minimizeWindow=function(){if("function"==typeof u.FC_ClickMinBtn)return u.FC_ClickMinBtn();console.log("\u6700\u5c0f\u5316\u7a97\u53e3")},i.directlyOpenGame=g,i.isFC_CloseLoadingPage=function(){if(void 0===window.external)return!1;try{return"function"==typeof u.FC_CloseLoadingPage}catch(e){return!1}}(),i.openFindGame=function(){if(void 0!==window.external){if("function"!=typeof u.FC_CloseLoadingPage)return console.log("FC_CloseLoadingPage"),!1;try{return u.FC_CloseLoadingPage()}catch(e){}}},i.openGame=function(e,t,n){return t=i.tracker.data({position_id:t,game_id:e.id,name:e.name,type:e.type,server_id:null==n?void 0:n.server_id}),g(f({game_id:e.id,server_id:null==n?void 0:n.server_id,statistical:t,gameInfo:e}))},i.onOpenGame=d("onOpenGame",500),i.getGlobalConfig=v;i.getGlobalConfigAsync=function(){if("function"==typeof u.FC_GetGlobalConfigAsync)return u.FC_GetGlobalConfigAsync();console.log("\u83b7\u53d6\u5f02\u6b65\u914d\u7f6e\u4fe1\u606f")},i.onGlobalConfigChangeAsync=d("onGlobalConfigChangeAsync"),i.openUpdateWindow=function(){if("function"==typeof u.FC_UpdateDlg)return u.FC_UpdateDlg();console.log("\u6253\u5f00\u66f4\u65b0\u7a97\u53e3")},i.openSettingsWindow=function(){if("function"==typeof u.FC_SettingsDlg)return u.FC_SettingsDlg();console.log("\u6253\u5f00\u8bbe\u7f6e\u7a97\u53e3")},i.openDownLoadManager=function(){if("function"==typeof u.FC_DownLoadManager)return u.FC_DownLoadManager();console.log("\u6253\u5f00\u4e0b\u8f7d\u7ba1\u7406\u7a97\u53e3")},i.removeClientGameFile=function(e){if("function"==typeof u.FC_DeleteGame)return u.FC_DeleteGame(JSON.stringify({game_id:String(e)}));console.log("\u5220\u9664\u6e38\u620f\u7684\u672c\u5730\u6570\u636e")},i.openInstallFlashWindow=function(){if("function"==typeof u.FC_InstallFlash)return u.FC_InstallFlash();console.log("\u6253\u5f00\u5b89\u88c5Flash\u7684\u7a97\u53e3")},i.openFlashSettingsWindow=function(){if("function"==typeof u.FC_FlashSettings)return u.FC_FlashSettings();console.log("\u6253\u5f00Flash\u8bbe\u7f6e\u7a97\u53e3")},i.runSwfFile=function(e){if("function"==typeof u.FC_OpenSWF)return u.FC_OpenSWF(e);console.log("\u6253\u5f00swf\u6587\u4ef6")},i.deleSwfFile=function(e){if("function"==typeof u.FC_ClearSWFRecorder)return u.FC_ClearSWFRecorder(e);console.log("\u5220\u9664swf\u6587\u4ef6")},i.openSwfSuccess=d("onFC_OpenSWF");var y,m=d("onGetSWFRecorder");i.getHistorySwf=function(){return"function"==typeof u.FC_GetSWFRecorder?new Promise(function(e){m.once(e),u.FC_GetSWFRecorder()}):(console.log("\u83b7\u53d6\u6253\u5f00\u8fc7\u7684swf\u5386\u53f2\u8bb0\u5f55"),Promise.resolve([]))},i.showChangePasswordWindow=function(){if("function"==typeof u.FC_ShowUpdatePasswd)return u.FC_ShowUpdatePasswd();console.log("\u4fee\u6539\u5bc6\u7801\u5f39\u7a97")},i.notifyClientChangePasswordSuccess=function(){if("function"==typeof u.FC_UpdatePasswdSuccess)return u.FC_UpdatePasswdSuccess();console.log("\u901a\u77e5\u5ba2\u6237\u7aef\u4fee\u6539\u5bc6\u7801\u6210\u529f")},i.openUrlByMiniBrowser=function(e){if("function"==typeof u.FC_OpenSearch)return u.FC_OpenSearch(e);console.log("\u5728Flash Player\u9875\u9762\u4e2d\u8f93\u5165\u6846\u6253\u5f00\u94fe\u63a5")},i.encrypt=function(e){return"function"==typeof u.FC_GetSign?JSON.parse(u.FC_GetSign(e)).sign:""},a.LoginSuccessEvent.on(function(){return r(void 0,void 0,void 0,function(){return o(this,function(e){switch(e.label){case 0:return[4,(0,a.getUserInfo)()];case 1:return y=e.sent(),[2]}})})}),a.LogoutSuccessEvent.on(function(){y=null});w.prototype.getUserUID=function(){return null==y?void 0:y.uid},w.prototype.getClientInfo=function(){var e=null!=(e=v())?e:{};return delete e.encryptuid,delete e.uid,delete e.channel,e},w.prototype.data=function(e){var t=this.getClientInfo();return f(n(n(n(n({},t),{channel_id:t.channel,layout:window.layoutID}),e),{uid:this.getUserUID(),time:Date.now()}))},w.prototype.event=function(e,t){e={event:e,key:0,statistical:this.data(t)};if("function"==typeof u.FC_PostCountData)return u.FC_PostCountData(JSON.stringify(e));console.log("\u53d1\u9001\u7edf\u8ba1",e)},w.prototype.page=function(e){this.event("loadpage",{page_id:e})},w.prototype.click=function(e){this.event("click",e)},w.prototype.clickGame=function(){for(var e,t,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];void 0!==n[0].position_id?this.event("click",{position_id:(e=n[0]).position_id,game_id:e.gameInfo.id,name:e.gameInfo.name,type:e.gameInfo.type,server_id:null==(t=n[1])?void 0:t.server_id}):this.event("click",{position_id:n[1],game_id:(e=n[0]).id,name:e.name,type:e.type,server_id:null==(t=n[2])?void 0:t.server_id})};t=w;function w(){}i.Tracker=t,i.tracker=new t,i.onFlashOpeResult=d("onFlashOpeResult"),i.startCheck=function(){if("function"==typeof u.FC_FlashOperate)return u.FC_FlashOperate(JSON.stringify({action:"StartCheck"}));console.log("\u5f00\u59cb\u68c0\u6d4b")},i.startRepair=function(e){if("function"==typeof u.FC_FlashOperate)return u.FC_FlashOperate(JSON.stringify({action:"StartRepair",RepairOptions:e}));console.log("\u5f00\u59cb\u4fee\u590d")},i.stopRepair=function(){if("function"==typeof u.FC_FlashOperate)return u.FC_FlashOperate(JSON.stringify({action:"StopRepair"}));console.log("\u505c\u6b62\u4fee\u590d")},i.notifyClientPageLoaded=function(){if("function"==typeof u.FC_FCWebInitFinish)return u.FC_FCWebInitFinish();console.log("\u9875\u9762\u52a0\u8f7d\u6210\u529f")},i.clearSearchRecord=function(e){if("function"==typeof u.FC_ClearBroRecorder)return e?u.FC_ClearBroRecorder(e):u.FC_ClearBroRecorder();console.log("\u6e05\u9664\u7528\u6237\u641c\u7d22\u8bb0\u5f55")},i.addSearchRecord=function(e){if("function"==typeof u.FC_AddBroRecorder)return u.FC_AddBroRecorder(JSON.stringify({url:e}));console.log("\u6dfb\u52a0\u641c\u7d22\u8bb0\u5f55")};var S=d("onGetBroRecorder");i.getSearchRecord=function(){return"function"==typeof u.FC_GetBroRecorder?new Promise(function(e){S.once(e),u.FC_GetBroRecorder()}):(console.log("\u83b7\u53d6\u641c\u7d22\u8bb0\u5f55"),Promise.resolve([]))};var _=d("onIsHsInFirewall");i.isHSInFirewall=function(){return"function"==typeof u.FC_IsHsInFirewall?new Promise(function(e){_.once(e),u.FC_IsHsInFirewall()}):(console.log("\u5224\u65adHS\u670d\u52a1\u662f\u5426\u5df2\u7ecf\u6dfb\u52a0\u5230\u9632\u706b\u5899"),Promise.resolve(0))};var C=d("onAddHsToFirewall");i.addHSToFirewall=function(){return"function"==typeof u.FC_AddHsToFirewall?new Promise(function(e){C.once(e),u.FC_AddHsToFirewall()}):(console.log("\u6dfb\u52a0HS\u670d\u52a1\u5230\u9632\u706b\u5899"),Promise.resolve(0))};var b=d("onNeedShowBootupRepairChk");i.needShowBootupRepairChk=function(){return"function"==typeof u.FC_NeedShowBootupRepairChk?new Promise(function(t){b.once(function(e){t(1==+e)}),u.FC_NeedShowBootupRepairChk()}):(console.log("\u662f\u5426\u9700\u8981\u5728\u4fee\u590d\u754c\u9762\u663e\u793a\u5f00\u542f\u81ea\u542f\u590d\u9009\u6846"),Promise.resolve(!0))},i.setBootupRepair=function(){"function"==typeof u.FC_FCSetBootupRepair?u.FC_FCSetBootupRepair():console.log("\u8bbe\u7f6e\u5f00\u673a\u81ea\u542f")},i.installCDM=function(e,t){"function"==typeof u.FC_StartInstallCDM?u.FC_StartInstallCDM(JSON.stringify({type:e,action:t})):console.log("\u5f00\u59cb\u5b89\u88c5\u6216\u66f4\u65b0CDM")};var x=d("onGetFlashStatus");i.getCDMStatus=function(){var e;return"function"==typeof u.FC_GetFlashStatus?new Promise(function(e){x.once(e),u.FC_GetFlashStatus()}):(console.log("\u83b7\u53d6CDM\u5b89\u88c5\u72b6\u6001"),Promise.resolve(((e={})[s.CDMType.AX]="",e[s.CDMType.PP]="",e[s.CDMType.NP]="",e)))},i.onInstallCDMProcess=d("onInstallCDMProcess"),i.openIEError=d("onOpenIEError"),i.openIe=function(){return"function"==typeof u.FC_OpenIE?new Promise(function(e){i.openIEError.once(function(){e(!1)}),u.FC_OpenIE()}):(console.log("IE\u6d4f\u89c8\u5668\u65b9\u6cd5\u4e0d\u5b58\u5728"),Promise.resolve(!1))},i.cefOderRequest=function(e){if("function"==typeof u.CefOderRequest)return u.CefOderRequest(JSON.stringify(e));console.log("CefOderRequest\u51fd\u6570\u4e0d\u5b58\u5728")},i.onOderResponse=d("onOderResponse")},function(e,s,t){"use strict";var o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},c=this&&this.__awaiter||function(e,a,s,c){return new(s=s||Promise)(function(n,t){function i(e){try{o(c.next(e))}catch(e){t(e)}}function r(e){try{o(c["throw"](e))}catch(e){t(e)}}function o(e){var t;e.done?n(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(i,r)}o((c=c.apply(e,a||[])).next())})},l=this&&this.__generator||function(i,r){var o,a,s,c={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},e={next:t(0),"throw":t(1),"return":t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(n){return function(e){var t=[n,e];if(o)throw new TypeError("Generator is already executing.");for(;c;)try{if(o=1,a&&(s=2&t[0]?a["return"]:t[0]?a["throw"]||((s=a["return"])&&s.call(a),0):a.next)&&!(s=s.call(a,t[1])).done)return s;switch(a=0,(t=s?[2&t[0],s.value]:t)[0]){case 0:case 1:s=t;break;case 4:return c.label++,{value:t[1],done:!1};case 5:c.label++,a=t[1],t=[0];continue;case 7:t=c.ops.pop(),c.trys.pop();continue;default:if(!(s=0<(s=c.trys).length&&s[s.length-1])&&(6===t[0]||2===t[0])){c=0;continue}if(3===t[0]&&(!s||t[1]>s[0]&&t[1]<s[3]))c.label=t[1];else if(6===t[0]&&c.label<s[1])c.label=s[1],s=t;else{if(!(s&&c.label<s[2])){s[2]&&c.ops.pop(),c.trys.pop();continue}c.label=s[2],c.ops.push(t)}}t=r.call(i,c)}catch(e){t=[6,e],a=0}finally{o=s=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}}},i=(s.__esModule=!0,s.logout=s.LogoutSuccessEvent=s.login=s.getUserInfo=s.LoginType=s.UserType=s.LoginSuccessEvent=s.CloseLoginEvent=void 0,n.prototype.on=function(e){return this.listeners.push(e),this},n.prototype.off=function(t){return this.listeners=void 0===t?[]:this.listeners.filter(function(e){return e!==t}),this},n.prototype.once=function(t){function n(e){i.off(n),t(e)}var i=this;return this.listeners.push(n),this},n.prototype.emit=function(t){return this.listeners.forEach(function(e){e(t)}),this},n);function n(){this.listeners=[]}var u=top.external;function r(e,t){void 0===t&&(t=0);var n=new i;return top[e]=function(e){setTimeout(function(){n.emit(e)},t)},n}s.CloseLoginEvent=r("onCloseLogin"),s.LoginSuccessEvent=r("onLoginSuccess");var a,f=r("onGetUserData");(a=s.UserType||(s.UserType={}))[a["Normal"]=1]="Normal",a[a["Temporary"]=2]="Temporary",a[a["QQ"]=3]="QQ",a[a["Wechat"]=4]="Wechat",(a=s.LoginType||(s.LoginType={}))["Login"]="0",a["Register"]="1";var d=null;function h(){var e;return d||((e=new Promise(function(t,e){f.once(function(e){1===(null==e?void 0:e.success)?t(null==e?void 0:e.userInfo):t(null)}),u.FC_GetUserData()})).then(function(){d=null}),d=e)}s.getUserInfo=h,s.login=function(n){var a=this;return new Promise(function(i,e){var r=function(n){return c(a,void 0,void 0,function(){var t;return l(this,function(e){switch(e.label){case 0:return s.LoginSuccessEvent.off(r),s.CloseLoginEvent.off(o),[4,h()];case 1:return(t=e.sent())?i([t,n]):i(null),[2]}})})},o=function(){s.LoginSuccessEvent.off(r),s.CloseLoginEvent.off(o),i(null)},t=(s.LoginSuccessEvent.on(r),s.CloseLoginEvent.on(o),{statistical:p(n)});u.FC_OpenLogin(JSON.stringify(t))})};var p=function(e){var t={};try{delete(t=JSON.parse(u.FC_GetGlobalConfig())).encryptuid,delete t.uid,delete t.channel}catch(e){}var n,i=o(o(o(o({},t),{channel_id:t.channel,layout:window.layoutID}),e),{time:Date.now()}),r={};for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&null!=i[n]&&(r[n]=i[n]);return r};s.LogoutSuccessEvent=r("onFC_Logout"),s.logout=function(r){var t=this;return new Promise(function(i,e){return c(t,void 0,void 0,function(){var t,n;return l(this,function(e){switch(e.label){case 0:return t=function(){s.LogoutSuccessEvent.off(t),i()},s.LogoutSuccessEvent.on(t),[4,h()];case 1:return n=e.sent(),n={statistical:p(o(o({},r),{uid:null==n?void 0:n.uid})),show_logindlg:"0"},u.FC_Logout(JSON.stringify(n)),[2]}})})})}},function(e,t,n){"use strict";var i;t.__esModule=!0,t.InstallCDMAction=t.CDMType=t.FlashRepairExceptionUIType=t.FlashRepairExceptionCategory=t.FlashRepairStatus=t.AccountType=t.LoginState=t.WindowState=t.GameStatus=t.GameType=t.CommonStatus=t.UsefulState=t.SupportLanguage=void 0,(i=t.SupportLanguage||(t.SupportLanguage={}))["zh_CN"]="zh-CN",i["en_US"]="en-US",(i=t.UsefulState||(t.UsefulState={}))["Initial"]="initial",i["Pending"]="pending",i["Complete"]="complete",i["Error"]="error",(i=t.CommonStatus||(t.CommonStatus={}))[i["Initial"]=0]="Initial",i[i["Success"]=1]="Success",i[i["Failure"]=2]="Failure",(i=t.GameType||(t.GameType={}))[i["Web"]=0]="Web",i[i["Client"]=1]="Client",i[i["Mini"]=2]="Mini",i[i["H5"]=3]="H5",(i=t.GameStatus||(t.GameStatus={}))[i["Hide"]=0]="Hide",i[i["Show"]=1]="Show",(i=t.WindowState||(t.WindowState={}))["Normal"]="0",i["Maximize"]="1",(i=t.LoginState||(t.LoginState={}))["NotLogin"]="0",i["Logined"]="1",(i=t.AccountType||(t.AccountType={}))[i["Normal"]=1]="Normal",i[i["Temporary"]=2]="Temporary",i[i["QQ"]=3]="QQ",i[i["Wechat"]=4]="Wechat",(i=t.FlashRepairStatus||(t.FlashRepairStatus={}))[i["Initial"]=0]="Initial",i[i["Checking"]=1]="Checking",i[i["EndCheck"]=2]="EndCheck",i[i["CancelCheck"]=3]="CancelCheck",i[i["Repairing"]=4]="Repairing",i[i["EndRepair"]=5]="EndRepair",i[i["CancelRepair"]=6]="CancelRepair",i[i["CancelingRepair"]=7]="CancelingRepair",(i=t.FlashRepairExceptionCategory||(t.FlashRepairExceptionCategory={}))[i["Unknown"]=0]="Unknown",i[i["Install"]=1]="Install",i[i["Region"]=2]="Region",i[i["LatestVersion"]=3]="LatestVersion",i[i["Running"]=4]="Running",i[i["AxInvalid"]=5]="AxInvalid",(i=t.FlashRepairExceptionUIType||(t.FlashRepairExceptionUIType={}))[i["Text"]=0]="Text",i[i["CheckBox"]=1]="CheckBox",i[i["Link"]=2]="Link",i[i["Function"]=3]="Function",(i=t.CDMType||(t.CDMType={}))["AX"]="ax",i["PP"]="pp",i["NP"]="np",(i=t.InstallCDMAction||(t.InstallCDMAction={}))["Install"]="install",i["Update"]="update",i["Reinstall"]="reinstall"},function(e,t,n){"use strict";var i,r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function o(e,t){if("function"==typeof external.FC_Cef_Function)return e=r({funcation_name:e},t||{}),external.FC_Cef_Function(JSON.stringify(e));console.error("\u8c03\u7528\u5ba2\u6237\u7aefFC_Cef_Function\u4e0d\u5b58\u5728")}t.__esModule=!0,t.onnlinePageUrl=t.openFlashToolBox=t.openFlashToolBoxHome=t.cefClientFn=t.ContainerToolStatus=t.ContainerBaseType=t.ContainerOpenType=void 0,(i=t.ContainerOpenType||(t.ContainerOpenType={}))[i["Tab"]=0]="Tab",i[i["Software"]=1]="Software",i[i["Web"]=2]="Web",i[i["Game"]=3]="Game",(i=t.ContainerBaseType||(t.ContainerBaseType={}))[i["Tab"]=0]="Tab",i[i["Software"]=1]="Software",i[i["Zip"]=2]="Zip",(i=t.ContainerToolStatus||(t.ContainerToolStatus={}))[i["NotPublish"]=0]="NotPublish",i[i["Published"]=1]="Published",i[i["Offline"]=2]="Offline",t.cefClientFn=o,t.openFlashToolBoxHome=function(e){o("CefStartFlashToolBoxHome",{params:r({openSetting:0},e)})},t.openFlashToolBox=function(e,t){o("CefStartFlashToolBox",{data:r(r({},e),{position_id:t})})},t.onnlinePageUrl="https://soft.flash.cn/aggregate-page/index.html#"}]);