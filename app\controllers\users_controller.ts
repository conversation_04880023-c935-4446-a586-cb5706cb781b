// import type { HttpContext } from '@adonisjs/core/http'

import { HttpContext } from "@adonisjs/core/http";
import { dd, getUserInfo, responseData } from "../function/helper.js";
import <PERSON>ie<PERSON> from "#models/tiezi";
import Zan from "#models/zan";
import Huifu from "#models/huifu";
import db from "@adonisjs/lucid/services/db";
import Shoucang from "#models/shoucang";
import FcGameHistoryV2 from "#models/game/fc_game_history_v2";
import UserSetting from "#models/user_setting";
import UserBan from "#models/user_ban";
import Notification from "#models/notification";

export default class UsersController {

    async info({ request, params, response }: HttpContext) {
        let authUser = request.user;
        let uid = request.input('uid');
        let user;
        if (uid) {
            user = await getUserInfo(uid);
        } else {
            user = await getUserInfo(authUser.uid);
        }
        if (!user) {
            return responseData(null, 400, "用户不存在");
        }

        // 查询或创建用户设置
        let showGameList = 0
        let hideBirthday = 0
        let hideCity = 0
        let userSetting = await UserSetting.query().where('user_id', user.uid).first();
        if (userSetting) {
            showGameList = userSetting.show_game_list;
            hideBirthday = userSetting.hide_birthday;
            hideCity = userSetting.hide_city;
        }

        let tiezi_list = await Tiezi.query().where('user_id', user.uid).where('status', 1);

        let tiezi_count = 0;
        let chuangzuo_count = 0;
        let total_zan = 0;
        let game_count = 0;
        let game_history: any[] = [];

        tiezi_list.forEach(async (tiezi) => {
            total_zan += tiezi.zan_num;
            if (tiezi.type == 1) {
                chuangzuo_count += 1;
            } else {
                tiezi_count += 1;
            }
        });

        // 获取游戏历史数据
        let gameHistoryMap = await FcGameHistoryV2.getUserData(user.uid);

        if (gameHistoryMap) {
            game_count = Object.keys(gameHistoryMap).length;
            // 根据游戏历史数据获取游戏列表, 按time倒序
            // 格式如下 {"153":{"duration":0,"lastOperateTimestamp":1673404682000,"like":0,"server_id":0,"time":1673404682000,"times":1}}
            for (let gameId in gameHistoryMap) {
                let historyInfo = gameHistoryMap[gameId];
                historyInfo.game_id = parseInt(gameId);
                game_history.push(historyInfo);
            }
            game_history.sort((a, b) => b.time - a.time);
        }
        if (!showGameList) {
            game_history = []
        }

        return responseData({
            nick: user.nick ? user.nick : user.username,
            avatar: user.avatar,
            age: user.age,
            area_info: user.area_info,
            male: user.male,
            introduce: user.introduce,
            border: user.border,
            show_game_list: showGameList,
            tiezi_count: tiezi_count,
            chuangzuo_count: chuangzuo_count,
            zan_count: total_zan,
            game_count: game_count,
            game_history: game_history,
            hide_birthday: hideBirthday,
            hide_city: hideCity
        }, 200);
    }

    async tieziList({ request, params, response }: HttpContext) {
        let user = request.user;
        let uid = request.input('uid');
        if (uid) {
            user = await getUserInfo(uid);
        }

        if (!user) {
            return responseData(null, 400, "用户不存在");
        }

        let page = request.input('page', 1);
        let pageSize = request.input('pageSize', 10);
        let type = request.input('type');

        let query = Tiezi.query().where('user_id', user.uid).where('status', '>=', 0);

        if (type !== undefined) {
            query.where('type', type);
        }

        let tiezi_list = await query.orderBy('id', 'desc').paginate(page, pageSize);

        await Promise.all(tiezi_list.map(async (tiezi) => {
            if (request.user?.uid) {
                await tiezi.setUserZan(request.user?.uid);
                await tiezi.setShoucang(request.user?.uid);
            }
        }));

        return responseData(tiezi_list, 200);
    }

    async huifuList({ request, params, response }: HttpContext) {
        let user = request.user;
        let page = request.input('page', 1);
        let pageSize = request.input('pageSize', 10);

        let query = Huifu.query()
            .where('user_id', user.uid);

        let huifu_list = await query.orderBy('id', 'desc').paginate(page, pageSize);
        // 获取tiezi
        await Promise.all(huifu_list.all().map(async (huifu) => {
            await huifu.setTiezi();
        }));

        return responseData(huifu_list, 200);
    }

    async zanList({ request, params, response }: HttpContext) {
        let user = request.user;
        let page = request.input('page', 1);
        let pageSize = request.input('pageSize', 10);

        let query = Zan.query()
            .where('user_id', user.uid);

        let zan_list = await query.orderBy('id', 'desc').paginate(page, pageSize);

        return responseData(zan_list, 200);
    }

    async shoucangList({ request, params, response }: HttpContext) {
        let user = request.user;
        let page = request.input('page', 1);
        let pageSize = request.input('pageSize', 10);

        let query = Shoucang.query()
            .where('user_id', user.uid);

        let shoucang_list = await query.orderBy('id', 'desc').preload('tiezi').paginate(page, pageSize);

        // 根据 收藏时间排序
        return responseData(shoucang_list, 200);
    }

    // 新增设置接口，更新用户是否对外展示游戏列表
    async updateUserSetting({ request, response }: HttpContext) {
        let user = request.user;
        if (!user) {
            return responseData(null, 400, "用户未登录");
        }

        let showGameList = request.input('show_game_list');
        let hideBirthday = request.input('hide_birthday');
        let hideCity = request.input('hide_city');

        let userSetting = await UserSetting.query().where('user_id', user.uid).first();
        if (!userSetting) {
            userSetting = new UserSetting();
            userSetting.user_id = user.uid;
        }
        userSetting.show_game_list = showGameList == 1 ? 1 : 0;
        userSetting.hide_birthday = hideBirthday == 1 ? 1 : 0;
        userSetting.hide_city = hideCity == 1 ? 1 : 0;
        await userSetting.save();

        return responseData(null, 200);
    }

    /**
     * 获取用户基础信息、禁言状态和未读消息数量
     */
    async baseInfo({ request, response }: HttpContext) {
        let user = request.user;
        if (!user) {
            return responseData(null, 400, "用户未登录");
        }

        // 获取用户设置
        let userSetting = await UserSetting.query().where('user_id', user.uid).first();

        // 检查用户是否被禁言
        const isBanned = await UserBan.isUserBanned(user.uid);

        // 获取未读消息数量
        const unreadCount = await Notification.query()
            .where('user_id', user.uid)
            .where('is_read', false)
            .count('* as total');

        const unreadTotal = unreadCount[0].$extras.total || 0;

        return responseData({
            uid: user.uid,
            username: user.username,
            nick: user.nick || user.username,
            avatar: user.avatar,
            border: user.border,
            male: user.male,
            age: user.age,
            type: user.type,
            area_info: user.area_info,
            is_banned: isBanned ? 1 : 0,
            unread_message_count: unreadTotal,
        }, 200);
    }

}
