import { HttpContext } from '@adonisjs/core/http'
import { syncVideos } from '../function/sync-videos.js'
import DuanjuVideo from '../models/duanju_video.js'
import DuanjuFavorite from '../models/duanju_favorite.js'
import { JuxingSDK, VideoEpisodeItem } from '../function/juxing-sdk.js'
import Cache from '../../function/Cache.js'
import DuanjuPlayback from '../models/duanju_playback.js'
import { dd } from '../../function/helper.js'
import { DateTime } from 'luxon'
import db from '@adonisjs/lucid/services/db'
import DuanjuWatchHistory from '../models/duanju_watch_history.js'

export default class DuanjuController {
  /**
   * 同步短剧视频数据
   */
  async syncVideos({ response }: HttpContext) {
    try {
      await syncVideos()
      return response.json({
        code: 200,
        msg: '同步成功'
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `同步失败1: ${error.message}`
      })
    }
  }

  /**
   * 获取短剧列表
   */
  async list({ request, response }: HttpContext) {
    try {
      const videos = await this.getCachedVideoList()
      return response.json({
        code: 200,
        msg: '获取成功',
        data: videos
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `获取失败: ${error.message}`
      })
    }
  }

  private async getCachedVideoList() {
    const cacheKey = 'duanju:list:all'
    let videos = await Cache.get(cacheKey)

    if (!videos) {
      videos = await DuanjuVideo.query()
        .where('status', 1)
        .orderBy('publish_time', 'desc')
      await Cache.set(cacheKey, videos, 180 * 1000)
    }

    return videos
  }

  /**
   * 获取视频详情
   */
  async detail({ request, response }: HttpContext) {
    try {
      const videoId = request.input('id')

      if (!videoId) {
        return response.status(400).json({
          code: 400,
          msg: '视频ID不能为空'
        })
      }

      const video = await DuanjuVideo.query()
        .where('id', videoId)
        .first()

      if (!video) {
        return response.status(404).json({
          code: 404,
          msg: '视频不存在'
        })
      }

      return response.json({
        code: 200,
        msg: '获取成功',
        data: video
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `获取失败: ${error.message}`
      })
    }
  }

  /**
   * 获取视频单集数据
   */
  async episode({ request, response, auth }: HttpContext) {
    try {
      const videoId = request.input('id')
      const episodeId = request.input('episodeId')
      const userId = request.user?.uid

      if (!userId) {
        return response.status(401).json({
          code: 401,
          msg: '请先登录'
        })
      }

      if (!videoId || !episodeId) {
        return response.status(400).json({
          code: 400,
          msg: '视频ID和分集ID不能为空'
        })
      }



      // 从播放记录中检测请求次数限制（每个用户最多10次）
      const oneHourAgo = DateTime.now().minus({ hours: 1 }).toSQL()
      const playbackCount = await DuanjuPlayback.query()
        .where('user_id', userId)
        .where('video_id', videoId)
        .where('episode_id', episodeId)
        .where('played_at', '>=', oneHourAgo)
        .count('* as count')
        .first()

      if (playbackCount && playbackCount.$extras.count >= 10) {
        return response.status(200).json({
          code: 400,
          msg: '操作过快，请稍后重试'
        })
      }

      // 先检查视频是否存在
      const video = await DuanjuVideo.query()
        .where('id', videoId)
        .first()

      if (!video) {
        return response.status(404).json({
          code: 404,
          msg: '视频不存在'
        })
      }

      // 检查列表缓存
      const cacheKey = `duanju:episodes:${video.video_id}`
      let episodes: VideoEpisodeItem[] = await Cache.get(cacheKey) || []

      if (episodes.length === 0) {
        // 调用SDK获取分集数据
        const sdk = new JuxingSDK()
        const result = await sdk.getVideoEpisodeList({
          videoId: video.video_id.toString(),
          expireTime: Math.floor(Date.now() / 1000) + 3600
        })

        episodes = result.data || []
        // 设置缓存
        await Cache.set(cacheKey, episodes, 3000 * 1000)
      }

      // 从缓存列表中查找指定分集
      const targetEpisode = episodes.find((item: VideoEpisodeItem) => item.episode_no.toString() === episodeId)

      if (!targetEpisode) {
        return response.status(404).json({
          code: 404,
          msg: '分集不存在'
        })
      }

      let data = {
        appID: targetEpisode.tcplayer_app_id,
        fileID: targetEpisode.tcplayer_file_id,
        psign: targetEpisode.tcplayer_sign_264,
        licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1314161253_1/v_cube.license'
      }

      // 添加播放记录到两个表
      try {
        // 1. 添加到详细播放记录表
        await DuanjuPlayback.create({
          user_id: userId,
          video_id: video.id,
          episode_id: Number(episodeId),
        })

        // 2. 更新观看历史表
        await DuanjuWatchHistory.updateHistory(
          userId,
          video.id,
          Number(episodeId)
        )
      } catch (err) {
        console.error('播放记录保存失败:', err)
      }

      return response.json({
        code: 200,
        msg: '获取成功',
        data
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `获取失败: ${error.message}`
      })
    }
  }

  public async index({ request, response }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 10)

    const videos = await DuanjuVideo.query()
      .where('status', 1)
      .orderBy('created_at', 'desc')
      .paginate(page, limit)

    return response.ok(videos)
  }

  /**
   * 搜索短剧（使用缓存数据）
   */
  async search({ request, response, auth }: HttpContext) {
    try {
      const keyword = request.input('keyword', '').trim()

      if (!keyword) {
        return response.status(400).json({
          code: 400,
          msg: '搜索关键词不能为空'
        })
      }

      // 使用 getCachedVideoList 方法获取数据
      const videos = await this.getCachedVideoList() as DuanjuVideo[]

      let filtered

      // 如果keyword是一个字
      if (keyword.length === 1) {
        filtered = videos.filter(video => {
          return video.video_title.toLowerCase().includes(keyword.toLowerCase())
        })
      } else {
        // 进行ngram=2分词
        const inputKeyword = keyword.trim();
        const tokens = this.ngram2(inputKeyword);
        const processedKeyword = tokens.join(' '); const results = await DuanjuVideo.query()
          .select('*', db.raw('MATCH(video_title) AGAINST(? IN NATURAL LANGUAGE MODE) as score', [processedKeyword]))
          .where('status', 1)
          .whereRaw("MATCH(video_title) AGAINST(? IN NATURAL LANGUAGE MODE)", [processedKeyword])
          .orderBy('score', 'desc')

        filtered = results
      }

      // 后面附加从标签中搜索
      filtered = filtered.concat(
        videos.filter(video => {
          const tagContent = `${video.tag_list}`.toLowerCase()
          return tagContent.includes(keyword.toLowerCase())
        })
      )

      return response.json({
        code: 200,
        msg: '搜索成功',
        data: filtered
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `搜索失败: ${error.message}`
      })
    }
  }

  private ngram2(text: string): string[] {
    const result: string[] = [];
    for (let i = 0; i < text.length - 1; i++) {
      result.push(text.substring(i, i + 2));
    }
    return result;
  }

  /**
   * @private
   */
  private parseVideoIds(videoIds: any): number[] {
    // 处理JSON格式的ID列表
    if (videoIds) {
      try {
        if (Array.isArray(videoIds)) {
          return videoIds.map(id => Number(id)).filter(id => !isNaN(id));
        } else if (!isNaN(Number(videoIds))) {
          return [Number(videoIds)];
        }
      } catch (error) {
        // JSON解析失败，返回空数组
      }
    }

    return [];
  }

  /**
   * 添加收藏（支持单个或批量）
   */
  async addFavorite({ request, response }: HttpContext) {
    try {
      const videoId = request.input('video_id')
      const videoIds = request.input('video_ids')
      const userId = request.user?.uid

      // 检查是否提供了视频ID（单个或批量）
      if (!videoId && !videoIds) {
        return response.status(400).json({
          code: 400,
          msg: '视频ID不能为空'
        })
      }

      // 如果有videoId，优先使用videoId
      if (videoId) {
        // 检查视频是否存在
        const video = await DuanjuVideo.find(videoId)
        if (!video) {
          return response.status(404).json({
            code: 404,
            msg: '视频不存在'
          })
        }

        // 检查是否已经收藏
        const existingFavorite = await DuanjuFavorite.query()
          .where('user_id', userId)
          .where('video_id', videoId)
          .first()

        if (existingFavorite) {
          return response.status(400).json({
            code: 400,
            msg: '已经收藏过该视频'
          })
        }

        // 创建收藏记录
        await DuanjuFavorite.create({
          user_id: userId,
          video_id: videoId
        })

        return response.json({
          code: 200,
          msg: '收藏成功'
        })
      }

      // 否则处理videoIds的批量收藏逻辑
      const ids = this.parseVideoIds(videoIds)

      // 验证ID数组
      if (ids.length === 0) {
        return response.status(400).json({
          code: 400,
          msg: '无效的视频ID列表'
        })
      }

      // 检查视频是否存在
      const videos = await DuanjuVideo.query()
        .whereIn('id', ids)

      if (videos.length !== ids.length) {
        return response.status(404).json({
          code: 404,
          msg: '部分视频不存在'
        })
      }

      // 检查哪些视频已经收藏
      const existingFavorites = await DuanjuFavorite.query()
        .where('user_id', userId)
        .whereIn('video_id', ids)

      // 过滤掉已经收藏的视频ID
      const existingIds = existingFavorites.map(fav => fav.video_id)
      const newIds = ids.filter(id => !existingIds.includes(Number(id)))

      // 如果所有视频都已收藏
      if (newIds.length === 0) {
        return response.status(400).json({
          code: 400,
          msg: '所有视频已经收藏过'
        })
      }

      // 批量创建收藏记录
      const favorites = newIds.map(videoId => ({
        user_id: userId,
        video_id: videoId
      }))

      await DuanjuFavorite.createMany(favorites)

      return response.json({
        code: 200,
        msg: '收藏成功',
        data: {
          total: ids.length,
          added: newIds.length,
          already_exists: existingIds.length
        }
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `收藏失败: ${error.message}`
      })
    }
  }

  /**
   * 取消收藏（支持单个或批量）
   */
  async removeFavorite({ request, response }: HttpContext) {
    try {
      const videoId = request.input('video_id')
      const videoIds = request.input('video_ids')
      const userId = request.user?.uid

      // 检查是否提供了视频ID（单个或批量）
      if (!videoId && !videoIds) {
        return response.status(400).json({
          code: 400,
          msg: '视频ID不能为空'
        })
      }

      // 如果有videoId，优先使用videoId
      if (videoId) {
        // 删除单个收藏记录
        await DuanjuFavorite.query()
          .where('user_id', userId)
          .where('video_id', videoId)
          .delete()

        return response.json({
          code: 200,
          msg: '取消收藏成功'
        })
      }

      // 否则处理videoIds的批量删除逻辑
      const ids = this.parseVideoIds(videoIds);

      // 验证ID数组
      if (ids.length === 0) {
        return response.status(400).json({
          code: 400,
          msg: '无效的视频ID列表'
        })
      }

      // 删除批量收藏记录
      const deleteCount = await DuanjuFavorite.query()
        .where('user_id', userId)
        .whereIn('video_id', ids)
        .delete()

      return response.json({
        code: 200,
        msg: '取消收藏成功',
        data: {
          removed: deleteCount
        }
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `取消收藏失败: ${error.message}`
      })
    }
  }

  /**
   * 获取收藏列表
   */
  async favoriteList({ request, response }: HttpContext) {
    try {
      const userId = request.user?.uid
      const favorites = await DuanjuFavorite.query()
        .where('user_id', userId)
        .orderBy('created_at', 'desc')

      // 修改返回的数据，包含 video_id 和 created_at
      const videoData = favorites.map((favorite) => ({ video_id: favorite.video_id, created_at: favorite.created_at }))

      return response.json({
        code: 200,
        msg: '获取成功',
        data: videoData
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `获取失败: ${error.message}`
      })
    }
  }


  /**
   * 获取观看历史列表
   */
  async watchHistory({ request, response }: HttpContext) {
    try {
      const userId = request.user?.uid
      const histories = await DuanjuWatchHistory.query()
        .where('user_id', userId)
        .orderBy('last_watched_at', 'desc')

      // 获取相关视频信息
      const videos = await this.getCachedVideoList() as DuanjuVideo[]

      // 合并视频信息到观看记录
      const data = histories.map(history => {
        const video = videos.find(v => v.id === history.video_id)
        if (!video) return null

        return {
          video_id: history.video_id,
          episode_id: history.last_episode_id,
          time: history.last_watched_at
        }
      }).filter(Boolean)

      return response.json({
        code: 200,
        msg: '获取成功',
        data
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `获取失败: ${error.message}`
      })
    }
  }

  /**
   * 删除观看历史
   */
  async deleteWatchHistory({ request, response }: HttpContext) {
    try {
      const userId = request.user?.uid
      const videoId = request.input('video_id')
      const videoIds = request.input('video_ids')

      // 检查是否提供了视频ID（单个或批量）
      if (!videoId && !videoIds) {
        return response.status(400).json({
          code: 400,
          msg: '视频ID不能为空'
        })
      }

      // 解析视频ID列表
      const ids = this.parseVideoIds(videoIds);

      // 验证ID数组
      if (ids.length === 0 && !videoId) {
        return response.status(400).json({
          code: 400,
          msg: '无效的视频ID列表'
        })
      }

      // 如果只有一个ID且是通过video_id参数传入，单独处理以保持原有逻辑
      if ((ids.length === 0 || (ids.length === 1 && videoId)) && videoId) {
        await DuanjuWatchHistory.query()
          .where('user_id', userId)
          .where('video_id', videoId)
          .delete()

        return response.json({
          code: 200,
          msg: '删除成功'
        })
      }

      // 批量删除观看历史记录
      const deleteCount = await DuanjuWatchHistory.query()
        .where('user_id', userId)
        .whereIn('video_id', ids)
        .delete()

      return response.json({
        code: 200,
        msg: '删除成功',
        data: {
          removed: deleteCount
        }
      })
    } catch (error) {
      return response.status(500).json({
        code: 500,
        msg: `删除失败: ${error.message}`
      })
    }
  }
}

