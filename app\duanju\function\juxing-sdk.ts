import axios from 'axios';
import crypto from 'crypto';
import { dd } from '../../function/helper.js';

interface ApiConfig {
    appId: string;
    appSecret: string;
    apiUrl: string;
}

interface VideoListParams {
    page?: number;
    pageSize?: number;
    updateTimeStart?: number;
    updateTimeEnd?: number;
}

interface VideoItem {
    video_id: number;
    video_total: number;
    video_title: string;
    logo_img: string;
    introduce: string;
    recommendation: string;
    tag_list: string[];
    year: number;
    is_recommend: number;
    is_end: number;
    is_new_video: number;
    buy_episode: number;
    duration: number;
    publish_time: number;
    wx_registration_number: string;
}

interface VideoListResponse {
    code: number;
    msg: string;
    data: {
        list: VideoItem[];
        pages: number;
    };
}

interface VideoMenuListParams {
    page?: number;
    pageSize?: number;
    updateTimeStart?: number;
    updateTimeEnd?: number;
}

interface VideoMenuListResponse {
    code: number;
    msg: string;
    data: {
        page: number;
        page_size: number;
        total: number;
        pages: number;
        list: Array<{
            menu_id: number;
            menu_name: string;
            video_ids: number[];
        }>;
    };
}

interface VideoEpisodeListParams {
    videoId: string;
    expireTime?: number;
}

interface VideoEpisodeListResponse {
    code: number;
    msg: string;
    data: Array<VideoEpisodeItem>;
}

export interface VideoEpisodeItem {
    video_id: number;
    episode_no: number;
    episode_title: string;
    episode_img: string;
    tcplayer_app_id: string;
    tcplayer_file_id: string;
    tcplayer_sign: string;
    expire_time: number;
    tcplayer_sign_265: string;
    tcplayer_sign_264: string;
}

function generateSignature(params: Record<string, any>, appSecret: string): string {
    const sortedParams = Object.keys(params)
        .sort()
        .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
        .map(key => `${key}=${params[key]}`)
        .join('&');

    const stringToSign = `${sortedParams}&app_secret=${appSecret}`;
    const hash = crypto.createHash('md5').update(stringToSign).digest('hex');
    return hash;
}

export class JuxingSDK {
    private config: ApiConfig;

    constructor() {
        this.config = {
            appId: 'bc0110373798',
            appSecret: 'adefc806a1c0938796628e11d873bec3',
            apiUrl: 'https://open-api.zjchjc.cn',
        }
    }

    private async request<T>(url: string, params: Record<string, any>): Promise<T> {
        const timestamp = Math.floor(Date.now() / 1000);

        params.app_id = this.config.appId;
        params.timestamp = timestamp;
        params.sign = generateSignature(params, this.config.appSecret);

        const queryParams = new URLSearchParams(params);

        const response = await axios.get<T>(`${this.config.apiUrl}${url}?${queryParams}`);


        return response.data;
    }

    public async getVideoList(params: VideoListParams = {}): Promise<VideoListResponse> {
        return this.request<VideoListResponse>('/api/open/video/list', params);
    }

    public async getVideoMenuList(params: VideoMenuListParams = {}): Promise<VideoMenuListResponse> {
        return this.request<VideoMenuListResponse>('/api/open/video/menu/list', params);
    }

    public async getVideoEpisodeList(params: VideoEpisodeListParams): Promise<VideoEpisodeListResponse> {
        return this.request<VideoEpisodeListResponse>('/api/open/video/episode/list', {
            video_id: params.videoId,
            expire_time: params.expireTime,
        });
    }
}