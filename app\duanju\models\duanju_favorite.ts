import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon'

export default class DuanjuFavorite extends BaseModel {
  static table = 'duanju_favorite' // 指定数据库表名

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare user_id: number

  @column()
  declare video_id: number

  @column.dateTime({ autoCreate: true })
  declare created_at: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updated_at: DateTime

} 