import { BaseModel, column, computed } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon'

export default class DuanjuVideo extends BaseModel {
  static table = 'duanju_video'

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare video_id: number

  @column()
  declare video_total: number

  @column()
  declare video_title: string

  @column()
  declare logo_img: string | null

  @column()
  declare introduce: string | null

  @column()
  declare recommendation: string | null

  @column()
  declare tag_list: string
  @column()
  declare year: number

  @column()
  declare is_recommend: boolean

  @column()
  declare is_end: boolean

  @column()
  declare is_new_video: boolean

  @column()
  declare buy_episode: number

  @column()
  declare duration: number

  @column()
  declare publish_time: number

  @column()
  declare wx_registration_number: string

  @column()
  declare status: number

  @column.dateTime({ autoCreate: true })
  declare created_at: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updated_at: DateTime

  // 计算 logo_img 去掉url后面的参数
  @computed()
  get raw_logo_img() {
    if (!this.logo_img) return null
    return this.logo_img.split('?')[0]
  }
}