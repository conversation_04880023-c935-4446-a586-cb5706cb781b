import { HttpContext } from "@adonisjs/core/http";
import axios from "axios";
import crypto from "crypto";
import env from '#start/env';
import Cache from "./Cache.js";


export function md5(str): string {
    const hash = crypto.createHash('md5');
    hash.update(str);
    return hash.digest('hex');
}


export function dd(data: any) {
    throw new Error(`print:${JSON.stringify(data)}`);
}


export function responseData(data: any, code: number = 200, msg: string = 'success') {
    let dataResponse = {
        code: code,
        msg: msg,
        data: data
    }
    return dataResponse;
}

export let str2StringArr = {
    prepare: (value: Array<number>) => value.join(','),
    consume: (value: string) => {
        if (!value) return [];
        let arr = value.split(',');
        return arr;
    }
}


export let str2NumArr = {
    prepare: (value: Array<number>) => value.join(','),
    consume: (value: string) => {
        if (!value) return [];
        let arr = String(value).split(',');
        return arr.map(Number);
    }
}

export async function getUserInfo(userId: number) {
    const cacheKey = `userInfo_${userId}`;
    return await Cache.wrap(cacheKey, async () => {
        let authurl = env.get('AUTHURL');

        const tokenCacheKey = 'token_internal_secret';
        let token = await Cache.wrap(tokenCacheKey, async () => {
            let tokenConfigUrl = 'http://apifcv2-lan.flash.cn/config/common?key=internal_secret';
            let tokenConfigRes = await axios.get(tokenConfigUrl);
            return tokenConfigRes.data.internal_secret;
        }, 60);

        let url = `${authurl}/api/userinfov1?user_id=${userId}&secret=${token}`;
        let res = await axios.get(url);
        let data = res.data;

        if (data.success) {
            const newUser = data.data;

            if (!newUser.avatar) {
                let avatarIndex = newUser.id % 6 + 1;
                newUser.avatar = `https://soft.flash.cn/lobby/assets/avatar/default${avatarIndex}-120x120.jpg`;
            }


            const userInfo = {
                uid: newUser.id,
                username: newUser.username,
                nick: newUser.nick,
                avatar: newUser.avatar,
                age: (() => {
                    let age = 0;
                    const birthday = newUser.ext?.birthday;
                    if (birthday) {
                        const birthDate = new Date(birthday);
                        const today = new Date();
                        age = today.getFullYear() - birthDate.getFullYear();
                        const m = today.getMonth() - birthDate.getMonth();
                        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                            age--;
                        }
                    }
                    return age;
                })(),
                border: newUser.ext?.border || "",
                area_info: newUser.ext?.area_info || "",
                male: newUser.ext?.male || "",
                introduce: newUser.ext?.introduce || "",
            };

            return userInfo;

        } else {
            return null;
        }
    }, 60);
}

export async function checkText(content: string) {
    const apiUrl = 'http://tool.flash.cn/sensitive';
    const requestUrl = `${apiUrl}?word=${encodeURIComponent(content)}`;
    try {
      const res = await axios.get(requestUrl);
      const result = res.data;
      if (!result.data || typeof result.data.is_sens === 'undefined') {
        return null;
      }
      return {
        isSensitive: result.data.is_sens,
        replacedContent: result.data.replace,
        originalContent: result.data.row_word
      };
    } catch (error) {
      return null;
    }
}