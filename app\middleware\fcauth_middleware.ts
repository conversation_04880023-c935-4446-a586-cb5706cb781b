import env from '#start/env';
import { HttpContext } from '@adonisjs/core/http'
import { NextFn } from '@adonisjs/core/types/http'
import axios from 'axios';
import { error } from 'console';
import { dd } from '../function/helper.js';


declare module '@adonisjs/core/http' {
	interface Request {
		user: any;
		fctoken: string;
	}
}


export default class FCAuthMiddleware {
	async handle({ request, response }: HttpContext, next: NextFn, options: { forceAuth?: boolean } = { forceAuth: true }) {
		let fctoken = request.input('fctoken') || request.header('fctoken') || request.cookiesList()['fctoken'] || request.cookiesList()['flashTokenyyds'] as string;

		let responseData = {
			code: 401,
			message: '未登录',
		};

		if (!fctoken && options.forceAuth) {
			return request.qs()['callback'] ? response.jsonp(responseData, request.qs()['callback']) : response.json(responseData);
		}

		let authurl = env.get('AUTHURL');
		let url = `${authurl}/api/user/userinfo?fctoken=${fctoken}`;
		let res = await axios.get(url);
		let data = res.data;


		if (data.success) {
			request.fctoken = fctoken;
			request.user = data.userInfo;
		} else if (options.forceAuth) {
			return request.qs()['callback'] ? response.jsonp(responseData, request.qs()['callback']) : response.json(responseData);
		}

		return next();
	}
}
