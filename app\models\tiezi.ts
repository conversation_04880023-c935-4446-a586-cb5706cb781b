import { DateTime } from 'luxon'
import { afterFind, afterFetch, BaseModel, CamelCaseNamingStrategy, column, computed } from '@adonisjs/lucid/orm'
import { dd, getUserInfo, str2NumArr } from '../function/helper.js';
import { disconnect } from 'process';
import db from '@adonisjs/lucid/services/db';
import env from '#start/env';
import axios from 'axios';
import Zan from './zan.js';
import Shoucang from './shoucang.js';

export default class Tiezi extends BaseModel {
    static table = 'tiezi';
    @column({ isPrimary: true })
    declare id: number

    @column() declare user_id: number;
    @column() declare title: string;
    @column() declare type: number;
    @column() declare game_id: number;
    @column() declare bankuai: number;
    @column() declare biaoqian: string;
    @column() declare text: string;
    @column() declare status: number;
    @column() declare images: string;


    @column()    declare zan_num: number;
    @column()    declare last_huifu_time:string;
    @column()    declare huifu_num: number;
    @column()    declare ban_huifu: number;

    @column()    declare is_official: number;
    @column()    declare show_in_all_topics: number;
    @column()    declare top_show: number;


    @column.dateTime({ autoCreate: true })
    declare created_at: DateTime

    // @column.dateTime({ autoCreate: true, autoUpdate: true })
    // declare updatedAt: DateTime


    async setImagesArr() {
        let ids = this.images.split(',').map(Number);
        if (ids.length === 0) {
            this.imagesArr = [];
            return;
        }
        let list = await db.connection('flash_center').from('cos').whereIn('id', ids);

        this.imagesArr = (await Promise.all(list)).filter(v => v);
    }

    async setUserInfo() {
        let userinfo = await getUserInfo(this.user_id)
        if (userinfo) {
            this.userinfo = userinfo;
        }
    }

    async setUserZan(uid) {
        if (!uid) return;
        let zan = await Zan.query().where('data_id', this.id).andWhere('user_id', uid).andWhere('type', 1).first();
        if (zan) {
            this.userZan = 1;
        }
    }

    async setShoucang(uid) {
        if (!uid) return;
        let shoucang = await Shoucang.query().where('tiezi_id', this.id).andWhere('user_id', uid).first();
        if (shoucang) {
            this.shoucang = 1;
        }
    }


    async complete(){
        await Promise.all([this.setImagesArr(),this.setUserInfo()]);
    }

    @afterFetch()
    static async afterFetchHook(tiezis: Tiezi[]) {
        for (const tiezi of tiezis) {
            await tiezi.complete()
        }
    }

    @afterFind()
    public static async afterFindHook(tiezi: Tiezi) {
      await tiezi.complete()
    }

    
    // @afterFind()
    // static async afterFindHook(tiezi: Tiezi) {
    //     let ids = tiezi.images.split(',').map(Number);
    //     let list = ids.map(async v => {
    //         let cosdata = await db.connection('flash_center').from('cos').where('id', v).first();
    //         return cosdata;
    //     });

    //     tiezi.imagesArr = await Promise.all(list);
    //     //return await Promise.all(list);
    // }



    @computed()    imagesArr: any[] = [];
    @computed()    userinfo: any;
    @computed()    userZan: any = 0;
    @computed()    shoucang: any = 0;


    // get imagesArr() {
    //     let ids = this.images.split(',').map(Number);
    //     let list = ids.map(async v => {
    //         let cosdata = await db.connection('flash_center').from('cos').where('id', v).first();
    //         return cosdata;
    //     })
    //     return Promise.all(list);
    //   let ids = this.images.split(',').map(Number);
    //   return ids.map(v => {
    //     return {
    //       id: v,
    //       url: `http://flash-center.oss-cn-beijing.aliyuncs.com/${v}`
    //     }
    //   })
    //}
}