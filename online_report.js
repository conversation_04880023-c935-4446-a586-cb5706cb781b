var wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
var wsUrl = wsProtocol + "//im-game.flash.cn/ws";

function getOnlineParams() {
    if (typeof (onlineReportParam) == 'undefined') {
        return ''
    } else {
        return onlineReportParam
    }
}
function websocketOnlineFunc() {
    var onlineParam = getOnlineParams();
    if (!onlineParam) {
        return;
    }

    var ws;
    var reconnectInterval = 5000;

    function connect() {
        try {
            ws = new WebSocket(wsUrl + '?' + onlineParam);
        } catch (e) {
            scheduleReconnect();
            return;
        }

        ws.onopen = function() {
            console.log('WebSocket 已连接');
        };

        ws.onmessage = function(evt) {
            ws.send(JSON.stringify({op: 3}));
        };

        ws.onclose = function() {
            console.warn('WebSocket 已关闭，' + reconnectInterval + 'ms 后重连');
            scheduleReconnect();
        };

        ws.onerror = function(err) {
            console.error('WebSocket 错误', err);
            ws.close();
        };
    }

    function scheduleReconnect() {
        setTimeout(function () {
            connect();
        }, reconnectInterval);
    }

    window.onunload = function() {
        if (ws) {
            ws.close();
        }
    };

    connect();
}

if (typeof (WebSocket) != 'undefined') {
    setTimeout(function () {
        websocketOnlineFunc();
    }, 60000);
}