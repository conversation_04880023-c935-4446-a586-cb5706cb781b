/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import env from './env.js'
import GamesController from '#controllers/games_controller';
import TestsController from '#controllers/tests_controller';
import { middleware } from './kernel.js';
import PlaysController from '#controllers/plays_controller';
import { throttle, userActionThrottle } from './limiter.js';
import db from '@adonisjs/lucid/services/db';
import { HttpContext } from '@adonisjs/core/http';
import Tiezis<PERSON>ontroller from '#controllers/tiezis_controller';
import ConfigsController from '#controllers/configs_controller';
import UsersController from '#controllers/users_controller';
import DuanjuController from '../app/duanju/controllers/duanju_controller.js';
import UserController from '../app/duanju/controllers/user_controller.js';
import NotificationsController from '#controllers/tongzhi_controller';
import TongzhiController from '#controllers/tongzhi_controller';

//router.on('/').render('pages/home')



router.group(() => {
    router.get('/', async ({ request, response }: HttpContext) => {
        return response.json({
            message: 'ok'
        })
    });

    router.group(() => {
        router.any('/fatie', [TiezisController, 'fatie']).use(middleware.fcauth()).use(middleware.banCheckMiddleware()).use(userActionThrottle);
        router.get('/zan', [TiezisController, 'zan']).use(middleware.fcauth()).use(userActionThrottle);
        router.get('/delete', [TiezisController, 'delete']).use(middleware.fcauth());
        router.any('/edit', [TiezisController, 'edit']).use(middleware.fcauth()).use(middleware.banCheckMiddleware()).use(userActionThrottle);

        router.get('/list', [TiezisController, 'list']).use(middleware.fcauth({ forceAuth: false }));
        router.get('/imageList', [TiezisController, 'imageList']);
        router.get('/detail', [TiezisController, 'detail']).use(middleware.fcauth({ forceAuth: false }));
        router.get('/search', [TiezisController, 'search']).use(middleware.fcauth({ forceAuth: false }));

        router.get('/listTest', [TiezisController, 'listTest']).use(middleware.fcauth({ forceAuth: false }));


        router.get('/huifu', [TiezisController, 'huifu']).use(middleware.fcauth()).use(middleware.banCheckMiddleware()).use(userActionThrottle);
        router.get('/deleteHuifu', [TiezisController, 'deleteHuifu']).use(middleware.fcauth());
        router.get('/huifuList', [TiezisController, 'huifuList']).use(middleware.fcauth({ forceAuth: false }))
        router.get('/shoucang', [TiezisController, 'shoucang']).use(middleware.fcauth()).use(userActionThrottle);

        router.get('/report', [TiezisController, 'report']).use(middleware.fcauth()).use(userActionThrottle);
        // 新增文本审核接口
        router.get('/auditText', [TiezisController, 'auditText']).use(middleware.fcauth({ forceAuth: true }));
    }).prefix('/tiezi');

    router.group(() => {
        router.get('/info', [UsersController, 'info']).use(middleware.fcauth({forceAuth: false}));
        router.get('/tieziList', [UsersController, 'tieziList']).use(middleware.fcauth({forceAuth: false}));
        router.get('/shoucangList', [UsersController, 'shoucangList']).use(middleware.fcauth());
        router.get('/huifuList', [UsersController, 'huifuList']).use(middleware.fcauth());
        router.get('/updateUserSetting', [UsersController, 'updateUserSetting']).use(middleware.fcauth());
        router.get('/baseInfo', [UsersController, 'baseInfo']).use(middleware.fcauth());
    }).prefix('/user')

        // 话题相关路由
        router.group(() => {
            router.get('/list', [TiezisController, 'topicList']).use(middleware.fcauth({forceAuth: false}))
            router.get('/update', [TiezisController, 'topicUpdate']).use(middleware.fcauth())
        }).prefix('/topic')

    router.group(() => {
        router.get('/common', [ConfigsController, 'common']);
        router.get('/zhuanqu', [ConfigsController, 'zhuanqu']);
    }).prefix('/config')


    // 短剧相关路由（包含基本接口和收藏接口）
    router.group(() => {
        // 基础短剧接口
        router.get('/list', [DuanjuController, 'list'])
        router.get('/detail', [DuanjuController, 'detail'])
        router.get('/episode', [DuanjuController, 'episode']).use(middleware.fcauth())

        // 用户VIP状态接口
        router.get('/user/vip', [UserController, 'vip']).use(middleware.fcauth()).use(middleware.cors())
        router.get('/search', [DuanjuController, 'search'])

        // 短剧收藏接口（嵌套路由）
        router.group(() => {
            router.any('/add', [DuanjuController, 'addFavorite']).use(middleware.fcauth())
            router.any('/remove', [DuanjuController, 'removeFavorite']).use(middleware.fcauth())
            router.get('/list', [DuanjuController, 'favoriteList']).use(middleware.fcauth())
        }).prefix('/favorite')

        // 添加播放记录路由
        router.get('/history', [DuanjuController, 'watchHistory']).use(middleware.fcauth())
        router.get('/history/delete', [DuanjuController, 'deleteWatchHistory']).use(middleware.fcauth())
    }).prefix('/duanju')

    // 通知相关路由
    router.group(() => {
        router.get('/list', [TongzhiController, 'list'])
        router.get('/unread-count', [TongzhiController, 'unreadCount'])
    }).prefix('/tongzhi').middleware(middleware.fcauth())

}).use(throttle)

router.get('/check', () => {
    return env.get('PORT') + ' ' + env.get('NODE_ENV') + ' ' + env.get('SESSION_DRIVER')
})

